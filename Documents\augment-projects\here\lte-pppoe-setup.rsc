# ========================================
# LTE TO PPPOE CLIENTS CONFIGURATION GUIDE
# Author: Sharon - Network Support Engineer
# Updated: December 2024
# ========================================

# OVERVIEW:
# This guide configures MikroTik router to:
# 1. Connect to internet via LTE
# 2. Provide PPPoE server for clients
# 3. Route client traffic through LTE connection

# ========================================
# STEP 1: LTE INTERFACE CONFIGURATION
# ========================================

## 1.1: Configure LTE Interface
# Insert SIM card and configure LTE interface
/interface lte set lte1 apn="internet" pin=""

# Enable LTE interface
/interface enable lte1

# Check LTE status
/interface lte monitor lte1

# Verify LTE gets IP address
/ip address print where interface=lte1

## 1.2: Set LTE as Default Route
# Add default route via LTE
/ip route add dst-address=0.0.0.0/0 gateway=lte1

# Enable masquerade for LTE
/ip firewall nat add chain=srcnat out-interface=lte1 action=masquerade

# ========================================
# STEP 2: BRIDGE SETUP FOR PPPOE CLIENTS
# ========================================

## 2.1: Create Bridge for PPPoE
# Create bridge for client connections
/interface bridge add name=pppoe-bridge

## 2.2: Add Ethernet Ports to Bridge
# Add ethernet ports where clients will connect
/interface bridge port add bridge=pppoe-bridge interface=ether2
/interface bridge port add bridge=pppoe-bridge interface=ether3
/interface bridge port add bridge=pppoe-bridge interface=ether4
/interface bridge port add bridge=pppoe-bridge interface=ether5

# Optional: Add wireless interface if needed
# /interface bridge port add bridge=pppoe-bridge interface=wlan1

## 2.3: Configure Bridge IP (Optional - for management)
# Add IP to bridge for local management
/ip address add address=*************/24 interface=pppoe-bridge

# ========================================
# STEP 3: PPPOE SERVER CONFIGURATION
# ========================================

## 3.1: Create IP Pool for PPPoE Clients
# Create IP pool for client assignments
/ip pool add name=pppoe-pool ranges=**********-************

## 3.2: Create PPPoE Server Profile
# Create profile with DNS and routing
/ppp profile add name=pppoe-profile \
    local-address=********** \
    remote-address=pppoe-pool \
    dns-server=*******,******* \
    use-compression=no \
    use-encryption=no

## 3.3: Enable PPPoE Server
# Enable PPPoE server on bridge
/interface pppoe-server server add \
    service-name=internet \
    interface=pppoe-bridge \
    default-profile=pppoe-profile \
    authentication=pap,chap \
    keepalive-timeout=60 \
    one-session-per-host=yes

# ========================================
# STEP 4: USER MANAGEMENT
# ========================================

## 4.1: Create PPPoE Users
# Add PPPoE users with credentials
/ppp secret add name=user1 password=pass1 profile=pppoe-profile
/ppp secret add name=user2 password=pass2 profile=pppoe-profile
/ppp secret add name=user3 password=pass3 profile=pppoe-profile

## 4.2: User with Bandwidth Limits
# Create profile with bandwidth limits
/ppp profile add name=pppoe-limited \
    local-address=********** \
    remote-address=pppoe-pool \
    dns-server=*******,******* \
    rate-limit=5M/20M

# Add user with limited bandwidth
/ppp secret add name=limited-user password=pass123 profile=pppoe-limited

# ========================================
# STEP 5: FIREWALL CONFIGURATION
# ========================================

## 5.1: Basic Firewall Rules
# Allow established and related connections
/ip firewall filter add chain=forward connection-state=established,related action=accept

# Allow PPPoE clients to access internet
/ip firewall filter add chain=forward in-interface-list=ppp out-interface=lte1 action=accept

# Drop invalid connections
/ip firewall filter add chain=forward connection-state=invalid action=drop

## 5.2: NAT Configuration
# Masquerade PPPoE traffic to LTE
/ip firewall nat add chain=srcnat src-address=**********/24 out-interface=lte1 action=masquerade

# ========================================
# STEP 6: QUALITY OF SERVICE (OPTIONAL)
# ========================================

## 6.1: Simple Queue for Bandwidth Management
# Create queue for all PPPoE users
/queue simple add name=pppoe-total target=**********/24 max-limit=50M/50M

## 6.2: Per-User Queues
# Individual user bandwidth control
/queue simple add name=user1-queue target=**********/32 max-limit=5M/10M
/queue simple add name=user2-queue target=**********/32 max-limit=3M/8M

# ========================================
# STEP 7: MONITORING AND VERIFICATION
# ========================================

## 7.1: Check LTE Status
/interface lte monitor lte1

## 7.2: Monitor PPPoE Sessions
/ppp active print

## 7.3: Check Interface Traffic
/interface monitor-traffic lte1,pppoe-bridge

## 7.4: Test Client Connectivity
# From client device, configure PPPoE connection:
# Username: user1
# Password: pass1
# Service: internet (or leave blank)

## 7.5: Verify Routing
/ip route print
/ping ******* count=5

# ========================================
# STEP 8: TROUBLESHOOTING COMMANDS
# ========================================

## 8.1: PPPoE Server Issues
# Check PPPoE server status
/interface pppoe-server server print

# Check PPPoE server logs
/log print where topics~"pppoe"

## 8.2: Authentication Issues
# Check PPP secrets
/ppp secret print

# Monitor authentication attempts
/log print where topics~"ppp"

## 8.3: LTE Connection Issues
# Check LTE signal strength
/interface lte info lte1

# Check LTE registration
/interface lte monitor lte1

## 8.4: Client Connection Issues
# Check active PPP sessions
/ppp active print detail

# Monitor PPP interface traffic
/interface monitor-traffic ppp-out1

# ========================================
# STEP 9: ADVANCED CONFIGURATION
# ========================================

## 9.1: RADIUS Authentication (Optional)
# Configure RADIUS for user authentication
/radius add address=************* secret=radius-secret service=ppp

# Enable RADIUS in PPP profile
/ppp profile set pppoe-profile use-radius=yes

## 9.2: VLAN Support for PPPoE
# Create VLAN interface for PPPoE
/interface vlan add name=pppoe-vlan vlan-id=100 interface=ether2

# Add VLAN to bridge
/interface bridge port add bridge=pppoe-bridge interface=pppoe-vlan

## 9.3: Backup Internet Connection
# Add secondary internet connection (ethernet WAN)
/ip route add dst-address=0.0.0.0/0 gateway=*********** distance=2

# ========================================
# STEP 10: MAINTENANCE COMMANDS
# ========================================

## 10.1: Disconnect Specific User
/ppp active remove [find name=user1]

## 10.2: Monitor Bandwidth Usage
/tool torch interface=lte1 duration=30

## 10.3: Backup Configuration
/export file=lte-pppoe-config

## 10.4: Reset PPPoE Server
/interface pppoe-server server disable [find]
/interface pppoe-server server enable [find]

# ========================================
# CONFIGURATION SUMMARY
# ========================================

# After completing this configuration:
# 1. LTE provides internet connectivity
# 2. PPPoE server accepts client connections on bridge
# 3. Clients get IP addresses from pool (**********-100)
# 4. All client traffic is routed through LTE
# 5. NAT masquerades client traffic
# 6. Basic firewall protects the network

# Client Configuration:
# - Connection Type: PPPoE
# - Username: user1 (or created username)
# - Password: pass1 (or created password)
# - Service Name: internet (optional)