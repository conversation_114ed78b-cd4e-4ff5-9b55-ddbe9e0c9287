# MikroTik Diagnostic Commands - ISP Troubleshooting Arsenal

## 🚀 Emergency Quick Check (30 seconds)

```bash
# System health snapshot
/system resource print
/system clock print
/system routerboard print

# Interface status overview
/interface print stats
/interface print status

# Critical connectivity
/ip route print where active
/ping ******* count=3
```

---

## 🔌 Physical Layer Diagnostics

### Interface Analysis
```bash
# Detailed interface statistics
/interface print stats-detail
/interface ethernet print status
/interface sfp print

# Monitor interface in real-time
/interface monitor-traffic [interface-name] duration=30

# Check for errors and drops
/interface print stats where rx-error>0 or tx-error>0
/interface print stats where rx-drop>0 or tx-drop>0

# Reset interface counters (for clean monitoring)
/interface reset-counters [interface-name]
```

### Cable and Physical Issues
```bash
# Ethernet auto-negotiation details
/interface ethernet print detail

# SFP module information (fiber links)
/interface sfp monitor [sfp-interface] duration=10

# Check power levels (fiber)
/interface sfp print detail where name=[interface-name]
```

---

## 🌐 Network Layer Diagnostics

### Routing Analysis
```bash
# Active routes only
/ip route print where active

# All routes with details
/ip route print detail

# Check for invalid routes
/ip route print where invalid

# Routing table for specific destination
/ip route print where dst-address~"***********/24"

# Default gateway verification
/ip route print where gateway=[gateway-ip]
```

### Connectivity Testing
```bash
# Basic ping with statistics
/ping [target-ip] count=10 size=64

# Continuous ping (Ctrl+C to stop)
/ping [target-ip] count=0

# Ping with source interface
/ping [target-ip] src-address=[source-ip] count=10

# Traceroute analysis
/traceroute [target-ip]

# Flood ping for performance testing
/tool flood-ping [target-ip] count=1000 size=1472
```

### ARP and MAC Address Issues
```bash
# ARP table examination
/ip arp print

# Find specific MAC address
/ip arp print where mac-address=[mac-address]

# Clear ARP entry
/ip arp remove [ip-address]

# Static ARP entries
/ip arp print where dynamic=no
```

---

## 🏷️ VLAN and Switching Diagnostics

### VLAN Configuration Check
```bash
# All VLAN interfaces
/interface vlan print detail

# Bridge configuration
/interface bridge print detail
/interface bridge port print detail

# VLAN filtering status
/interface bridge vlan print

# Check bridge MAC table
/interface bridge host print
```

### Switching Issues
```bash
# Bridge port states
/interface bridge port print where disabled=no

# Monitor bridge traffic
/interface bridge port monitor [port-number] duration=10

# Check for bridge loops
/interface bridge settings print
/interface bridge port print where edge=no
```

---

## 🌍 Internet and Upstream Diagnostics

### DHCP Client Status (for upstream)
```bash
# DHCP client status
/ip dhcp-client print detail

# Renew DHCP lease
/ip dhcp-client renew [interface]

# Release and renew
/ip dhcp-client release [interface]
/ip dhcp-client renew [interface]
```

### DNS Diagnostics
```bash
# DNS settings
/ip dns print

# DNS cache contents
/ip dns cache print

# Clear DNS cache
/ip dns cache flush

# Test DNS resolution
/ping google.com count=3
/tool nslookup google.com
```

### Bandwidth Testing
```bash
# Bandwidth test to upstream
/tool bandwidth-test [upstream-ip] protocol=tcp direction=both duration=30s

# UDP bandwidth test
/tool bandwidth-test [upstream-ip] protocol=udp direction=transmit duration=10s

# Test with different packet sizes
/tool bandwidth-test [upstream-ip] protocol=tcp local-tx-speed=100M duration=20s
```

---

## 🔥 Firewall and Security Diagnostics

### Connection Tracking
```bash
# Active connections count
/ip firewall connection print count-only

# Connections by protocol
/ip firewall connection print where protocol=tcp
/ip firewall connection print where protocol=udp

# Find connections to specific IP
/ip firewall connection print where src-address~"*************"

# Connection states
/ip firewall connection print where connection-state=established
```

### Firewall Rules Analysis
```bash
# Filter rules with packet counts
/ip firewall filter print stats

# NAT rules with statistics
/ip firewall nat print stats

# Mangle rules (QoS/marking)
/ip firewall mangle print stats

# Find rules with zero hits (unused rules)
/ip firewall filter print stats where packets=0
```

---

## 📊 Performance and Monitoring

### System Performance
```bash
# CPU and memory usage
/system resource print

# Process list
/system resource cpu print

# Disk usage
/system resource storage print

# Temperature monitoring
/system health print
```

### Traffic Analysis
```bash
# Interface traffic over time
/tool graphing interface print

# Real-time traffic monitoring
/interface monitor-traffic [interface] duration=60

# Traffic by protocol
/tool traffic-monitor interface=[interface] duration=30
```

### Historical Data
```bash
# System logs
/log print where topics~"error|critical|warning"

# Log by time period
/log print where time>="jan/15/2024 10:00:00"

# Interface specific logs
/log print where message~"[interface-name]"
```

---

## 🔧 Quick Fixes and Resets

### Interface Resets
```bash
# Disable/enable interface
/interface ethernet set [interface] disabled=yes
/interface ethernet set [interface] disabled=no

# Reset interface statistics
/interface reset-counters [interface]

# Force interface speed/duplex
/interface ethernet set [interface] speed=1Gbps duplex=full
```

### Service Restarts
```bash
# Restart DHCP client
/ip dhcp-client release [interface]
/ip dhcp-client renew [interface]

# Restart DNS
/ip dns cache flush

# Restart routing (careful!)
/routing ospf instance disable [instance]
/routing ospf instance enable [instance]
```

---

## 📋 Diagnostic Scripts

### Create monitoring script
```bash
# Add to scheduler for regular checks
/system script add name="health-check" source={
    :log info "=== Health Check Start ==="
    :log info ("CPU: " . [/system resource get cpu-load] . "%")
    :log info ("Memory: " . [/system resource get free-memory] . " bytes free")
    :log info ("Uptime: " . [/system resource get uptime])
    :foreach i in=[/interface find] do={
        :local iname [/interface get $i name]
        :local istatus [/interface get $i running]
        :if ($istatus = false) do={
            :log warning ("Interface " . $iname . " is DOWN")
        }
    }
    :log info "=== Health Check End ==="
}

# Schedule to run every 5 minutes
/system scheduler add name="health-monitor" interval=5m on-event="health-check"
```

This command reference gives you the tools to quickly diagnose any network issue. Practice these commands regularly to build speed and confidence in your troubleshooting abilities.
