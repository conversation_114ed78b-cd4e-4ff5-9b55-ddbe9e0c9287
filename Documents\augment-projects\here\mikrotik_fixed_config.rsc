# Fixed MikroTik Configuration

# Create bridge
/interface bridge
add name=bridgeLocal

# Add ports to bridge
/interface bridge port
add bridge=bridgeLocal interface=ether2
add bridge=bridgeLocal interface=ether3
add bridge=bridgeLocal interface=ether4
add bridge=bridgeLocal interface=ether5

# Add IP to bridge
/ip address
add address=*********/21 interface=bridgeLocal network=*********

# DNS
/ip dns
set allow-remote-requests=yes servers=*******,*******,*******,*******

# DHCP client on WAN
/ip dhcp-client
add interface=ether1 disabled=no

# Create interface lists for NAT
/interface list
add name=WAN
/interface list member
add interface=ether1 list=WAN

# NAT for both WAN and VPN
/ip firewall nat
add action=masquerade chain=srcnat out-interface-list=WAN

# IP pools
/ip pool
add name=hs-pool-6 ranges=**********-***********
add name=PPPOE_POOL ranges=************-*************
add name=GUEST_POOL ranges=************/23
add name=ROGUE_POOL ranges=************/23

# DHCP server
/ip dhcp-server
add address-pool=hs-pool-6 interface=bridgeLocal name=dhcp1

/ip dhcp-server network
add address=*********/21 comment="hotspot network" gateway=*********

# Hotspot profile
/ip hotspot profile
add hotspot-address=********* name=hsprof1

# Hotspot user profiles
/ip hotspot user profile
add name=skysyncguest rate-limit=1M/1M
add name=quarantine

# Hotspot server
/ip hotspot
add address-pool=hs-pool-6 disabled=no interface=bridgeLocal name=skysync profile=hsprof1

# PPP profiles
/ppp profile
add dns-server=*******,******* local-address=PPPOE_POOL name=skysyncdefault remote-address=PPPOE_POOL
add local-address=GUEST_POOL name=skysyncguest rate-limit=1M/1M remote-address=GUEST_POOL
add local-address=ROGUE_POOL name=quarantine rate-limit=1M/1M remote-address=ROGUE_POOL

# PPPoE server
/interface pppoe-server server
add authentication=pap default-profile=skysyncdefault disabled=no interface=bridgeLocal service-name=skysyncpppoe

# OVPN client
/interface ovpn-client
add certificate=skysync1.ovpn_1 cipher=aes128-cbc connect-to=*************** name=avitechcloud password=AviSoll user=skysync1

# Routes - CRITICAL: Add route to VPN server first, then default route
/ip route
add dst-address=***************/32 gateway=[/ip dhcp-client get [find interface=ether1] gateway]
add dst-address=0.0.0.0/0 gateway=avitechcloud

# Add VPN to WAN list after it's created
/interface list member
add interface=avitechcloud list=WAN