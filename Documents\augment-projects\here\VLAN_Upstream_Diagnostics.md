# VLAN & Upstream Link Diagnostics - Advanced ISP Troubleshooting

## 🏷️ VLAN Troubleshooting Mastery

### Understanding VLAN Architecture
```
[Customer] → [Access Switch] → [Distribution Router] → [Upstream Provider]
    |              |                    |                     |
Untagged      Tagged VLAN         Tagged VLAN           Provider VLAN
```

### Essential VLAN Commands
```bash
# Complete VLAN overview
/interface vlan print detail
/interface bridge vlan print
/interface bridge port print detail

# VLAN traffic monitoring
/interface monitor-traffic [vlan-interface] duration=30

# Bridge MAC learning
/interface bridge host print where vid=[vlan-id]
```

---

## 🔍 VLAN Issue Diagnosis

### Scenario 1: Customer VLAN Not Working

#### Step 1: Verify VLAN Configuration (30 seconds)
```bash
# Check VLAN interface exists
/interface vlan print where name=[customer-vlan]

# Verify VLAN ID matches upstream
/interface vlan print detail where name=[customer-vlan]

# Check VLAN is UP
/interface print status where name=[customer-vlan]
```

#### Step 2: Check Bridge Configuration (1 minute)
```bash
# Verify bridge VLAN filtering
/interface bridge print detail where vlan-filtering=yes

# Check VLAN membership
/interface bridge vlan print where vlan-ids~"[vlan-id]"

# Verify port VLAN assignment
/interface bridge port print where pvid=[vlan-id]
```

#### Step 3: Test VLAN Connectivity (2 minutes)
```bash
# Ping within VLAN
/ping [vlan-gateway] src-address=[vlan-ip] count=10

# Check VLAN routing
/ip route print where dst-address=[vlan-subnet]

# Monitor VLAN traffic
/interface monitor-traffic [vlan-interface] duration=30
```

### Common VLAN Problems & Solutions

#### Problem 1: VLAN ID Mismatch
**Symptoms:** No connectivity despite physical link up
```bash
# Check configured VLAN ID
/interface vlan print detail where name=[vlan-interface]

# Verify with upstream provider
# Solution: Correct VLAN ID
/interface vlan set [vlan-interface] vlan-id=[correct-vlan-id]
```

#### Problem 2: Missing Bridge VLAN Entry
**Symptoms:** VLAN interface up but no traffic
```bash
# Check bridge VLAN table
/interface bridge vlan print where vlan-ids=[vlan-id]

# Solution: Add VLAN to bridge
/interface bridge vlan add bridge=[bridge] vlan-ids=[vlan-id] tagged=[trunk-ports] untagged=[access-ports]
```

#### Problem 3: Incorrect Port VLAN Assignment
**Symptoms:** Customer on wrong VLAN
```bash
# Check port VLAN assignment
/interface bridge port print where interface=[customer-port]

# Solution: Correct PVID
/interface bridge port set [customer-port] pvid=[correct-vlan]
```

---

## 🌐 Upstream Provider Link Diagnostics

### Understanding Upstream Architecture
```
Your Router → Provider Edge → Provider Core → Internet
     |              |              |            |
  Your WAN      Provider LAN    Provider WAN   Global
```

### Essential Upstream Commands
```bash
# Upstream interface status
/interface print stats where name~"wan|uplink|upstream"

# Provider gateway connectivity
/ping [provider-gateway] count=20 size=64

# Traceroute to internet
/traceroute *******

# Bandwidth test to provider
/tool bandwidth-test [provider-ip] protocol=tcp direction=both duration=30s
```

---

## 🔧 Upstream Issue Diagnosis

### Scenario 1: Upstream Link Performance Issues

#### Step 1: Baseline Performance (2 minutes)
```bash
# Test current bandwidth
/tool bandwidth-test [upstream-gateway] protocol=tcp direction=both duration=30s

# Check interface utilization
/interface monitor-traffic [wan-interface] duration=60

# Test latency and packet loss
/tool flood-ping [upstream-gateway] count=1000 size=64
```

#### Step 2: Identify Bottlenecks (3 minutes)
```bash
# Check for interface errors
/interface print stats-detail where name=[wan-interface]

# Monitor queue depths
/queue simple print stats
/queue tree print stats

# Check connection tracking
/ip firewall connection print count-only
```

#### Step 3: Provider Path Analysis (2 minutes)
```bash
# Detailed traceroute
/traceroute ******* count=3

# Test multiple destinations
/traceroute *******
/traceroute [customer-destination]

# Check BGP if applicable
/routing bgp peer print status
```

### Scenario 2: Upstream Authentication Issues

#### DHCP Authentication Problems
```bash
# Check DHCP client status
/ip dhcp-client print detail where interface=[wan-interface]

# View DHCP options received
/ip dhcp-client option print

# Release and renew lease
/ip dhcp-client release [wan-interface]
/ip dhcp-client renew [wan-interface]
```

#### PPPoE Authentication Issues
```bash
# Check PPPoE client status
/interface pppoe-client print detail

# View connection logs
/log print where topics~"ppp" and time>="today"

# Restart PPPoE connection
/interface pppoe-client disable [pppoe-interface]
/interface pppoe-client enable [pppoe-interface]
```

---

## 📊 Advanced VLAN Monitoring

### Real-Time VLAN Traffic Analysis
```bash
# Monitor all VLANs simultaneously
/interface monitor-traffic [vlan1],[vlan2],[vlan3] duration=60

# VLAN-specific bandwidth usage
/tool traffic-monitor interface=[vlan-interface] duration=30

# Bridge port traffic by VLAN
/interface bridge port monitor [port] duration=30
```

### VLAN Performance Scripting
```bash
# Create VLAN monitoring script
/system script add name="vlan-monitor" source={
    :foreach vlan in=[/interface vlan find] do={
        :local vname [/interface vlan get $vlan name]
        :local vstats [/interface get $vlan]
        :if ([:pick $vstats 0 2] = "rx") do={
            :log info ("VLAN " . $vname . " RX: " . [:pick $vstats 3 -1])
        }
    }
}

# Schedule regular VLAN checks
/system scheduler add name="vlan-check" interval=5m on-event="vlan-monitor"
```

---

## 🚨 Upstream Provider Escalation

### Information to Gather Before Calling Provider

#### Circuit Information
- Circuit ID / Service ID
- Account number
- Technical contact information

#### Technical Details
```bash
# Interface statistics
/interface print stats-detail where name=[wan-interface] > interface-stats.txt

# Traceroute results
/traceroute [destination] > traceroute-results.txt

# Bandwidth test results
/tool bandwidth-test [provider-gateway] duration=60s > bandwidth-test.txt

# Error logs
/log print where topics~"error|critical" and time>="today" > error-logs.txt
```

#### Timeline Documentation
- Exact time issue started
- Duration of outage
- Intermittent vs. constant problem
- Customer impact assessment

### Provider Escalation Script
```
"Hello, this is [Your Name] from [Company]. We're experiencing issues with circuit [Circuit-ID].

Issue: [Brief description]
Started: [Exact time]
Impact: [Number of customers affected]

Technical details:
- Interface shows [status]
- Ping to your gateway: [results]
- Traceroute stops at: [hop number/IP]
- Bandwidth test shows: [results]

We've verified our equipment is functioning correctly. Can you check your side?"
```

---

## 🔄 VLAN Troubleshooting Flowchart

```
VLAN Issue Reported
├── Physical link UP?
│   ├── NO → Check Layer 1 (cables, SFP, power)
│   └── YES → Continue
├── VLAN interface UP?
│   ├── NO → Check VLAN configuration
│   └── YES → Continue
├── Can ping VLAN gateway?
│   ├── NO → Check routing/bridge config
│   └── YES → Continue
├── Can reach internet via VLAN?
│   ├── NO → Check upstream/NAT
│   └── YES → Application layer issue
```

---

## 💡 Pro Tips for VLAN & Upstream Issues

### 1. Document Your VLAN Scheme
```bash
# Create VLAN documentation
VLAN 10: Management (************/24)
VLAN 20: Customer A (*********/24)
VLAN 30: Customer B (*********/24)
VLAN 100: Upstream Provider
```

### 2. Use Consistent Naming
```bash
# Standardize interface names
/interface vlan set [vlan] name="vlan-[id]-[description]"
# Example: vlan-20-customer-a
```

### 3. Monitor Key Metrics
- VLAN utilization per customer
- Upstream bandwidth usage
- Error rates on trunk ports
- Provider SLA compliance

### 4. Automate Common Checks
```bash
# Daily VLAN health check
/system script add name="daily-vlan-check" source={
    :foreach vlan in=[/interface vlan find] do={
        :local vname [/interface vlan get $vlan name]
        :local running [/interface get $vlan running]
        :if (!$running) do={
            :log warning ("VLAN " . $vname . " is DOWN")
        }
    }
}
```

This specialized guide will help you quickly diagnose and resolve complex VLAN and upstream connectivity issues that are common in ISP environments.
