# ========================================
# MIKROTIK NETWORK TROUBLESHOOTING GUIDE
# Author: Sharon - Network Support Engineer
# Updated: December 2024
# ========================================

# ========================================
# SECTION 1: DEVICE CONNECTIVITY ISSUES
# ========================================

## STEP 1: BASIC CONNECTIVITY CHECK
# Purpose: Verify if device is reachable and responding

# Test basic ping connectivity
/ping <target-ip> count=5

# Check if device responds to different IPs in same subnet
/ping <subnet-ip-1> count=3
/ping <subnet-ip-2> count=3

## STEP 2: LAYER 2 VERIFICATION
# Purpose: Confirm device presence at MAC level

# Scan for MAC addresses on interface
/tool mac-scan interface=<interface-name>
# Look for: Target MAC address and IP assignment

# Check ARP table for device
/ip arp print where address=<target-ip>
# Status should be: "reachable" or "complete"
# If "failed" or "incomplete" - ARP issue exists

## STEP 3: INTERFACE CONFIGURATION CHECK
# Purpose: Verify interface setup and IP addressing

# Check interface status and configuration
/interface print where name=<interface-name>
/interface print stats where name=<interface-name>

# Verify IP address configuration
/ip address print where interface=<interface-name>

# For VLAN interfaces, check VLAN configuration
/interface vlan print where name=<interface-name>

## STEP 4: BRIDGE CONFIGURATION (If Applicable)
# Purpose: Check if interface is bridged and bridge status

# Check if interface is bridge slave
/interface bridge port print where interface=<interface-name>

# If bridged, check bridge IP addresses
/ip address print where interface=<bridge-name>

# Check bridge ARP table
/ip arp print where interface=<bridge-name>

## STEP 5: ARP RESOLUTION FIXES
# Purpose: Resolve ARP-related connectivity issues

# Remove failed ARP entries
/ip arp remove [find address=<target-ip>]

# Add static ARP entry (use correct interface - bridge if bridged)
/ip arp add address=<target-ip> mac-address=<target-mac> interface=<correct-interface>

# Verify static ARP entry
/ip arp print where address=<target-ip>

# Test connectivity after ARP fix
/ping <target-ip> count=5

## STEP 6: MANAGEMENT ACCESS TESTING
# Purpose: Test different management protocols

# Test web interface
# Browser: http://<target-ip>

# Test Winbox (try both methods)
# Winbox: Connect to <target-ip>
# Winbox: Connect to <target-mac>

# Test SSH
/system ssh <target-ip>

# Test Telnet
/system telnet <target-ip>

# ========================================
# SECTION 2: BANDWIDTH & PERFORMANCE ISSUES
# ========================================

## STEP 1: INTERFACE TRAFFIC MONITORING
# Purpose: Identify traffic patterns and bottlenecks

# Monitor interface traffic in real-time
/interface monitor-traffic <interface-name> duration=60
# Look for: TX vs RX rates, packet drops, errors

# Check interface statistics
/interface print stats where name=<interface-name>

# Monitor FastPath usage (should be enabled for physical interfaces)
# FastPath TX = 0 on physical interfaces indicates issues

## STEP 2: QUEUE AND BANDWIDTH LIMITS
# Purpose: Check if traffic shaping is affecting performance

# Check simple queues
/queue simple print where target~<interface-name>
/queue simple print stats

# Look for:
# - max-limit vs current rate
# - queued-packets (should be 0)
# - dropped packets

## STEP 3: PHYSICAL LAYER VERIFICATION
# Purpose: Ensure optimal physical connectivity

# Check ethernet interface status
/interface ethernet monitor <interface-name> once
# Verify: link-ok, full-duplex, correct speed

# Check for interface errors
/interface print stats-detail where name=<interface-name>

# Check system logs for interface issues
/log print where topics~"interface"

## STEP 4: TRAFFIC ANALYSIS
# Purpose: Analyze traffic patterns and identify issues

# Real-time traffic monitoring
/tool torch interface=<interface-name> duration=30

# Packet capture for detailed analysis
/tool sniffer quick interface=<interface-name> duration=30

# Bandwidth testing
/tool bandwidth-test <target-ip> protocol=tcp direction=both duration=20

# ========================================
# SECTION 3: ROUTING & FIREWALL ISSUES
# ========================================

## STEP 1: ROUTING TABLE VERIFICATION
# Purpose: Ensure correct routing paths exist

# Check routing table
/ip route print

# Check specific route to destination
/ip route check <destination-ip>

# Verify default gateway
/ip route print where dst-address=0.0.0.0/0

## STEP 2: FIREWALL RULE ANALYSIS
# Purpose: Identify blocking firewall rules

# Check firewall filter rules
/ip firewall filter print

# Look for invalid rules (marked with "I")
/ip firewall filter remove [find invalid]

# Check NAT rules
/ip firewall nat print

## STEP 3: SERVICE ACCESSIBILITY
# Purpose: Verify management services are enabled

# Check IP services
/ip service print

# Enable Winbox if disabled
/ip service enable winbox

# Check MAC server settings
/tool mac-server print
/tool mac-server mac-winbox print

# ========================================
# SECTION 4: CLEANUP & RECOVERY PROCEDURES
# ========================================

## STEP 1: REMOVE TEMPORARY CONFIGURATIONS
# Purpose: Clean up test configurations safely

# Remove temporary IP addresses
/ip address remove [find comment~"temp"]

# Remove failed ARP entries
/ip arp remove [find status="failed"]

# Remove temporary firewall rules
/ip firewall filter remove [find comment~"temp"]

## STEP 2: RESTORE ORIGINAL CONFIGURATION
# Purpose: Revert to known working state

# Backup current configuration before changes
/export file=backup-before-changes

# Document all changes made during troubleshooting
# Keep log of:
# - Commands executed
# - Results observed
# - Final resolution

## STEP 3: VERIFICATION AFTER FIXES
# Purpose: Confirm issues are resolved

# Test basic connectivity
/ping <target-ip> count=5

# Verify management access
# Test: Winbox, SSH, Web interface

# Check interface statistics
/interface print stats where name=<interface-name>

# Monitor for stability
/interface monitor-traffic <interface-name> duration=30

# ========================================
# SECTION 5: COMMON ISSUE QUICK FIXES
# ========================================

## ARP Resolution Issues:
/ip arp remove [find address=<ip>]
/ip arp add address=<ip> mac-address=<mac> interface=<interface>

## Bridge vs VLAN Interface Confusion:
# Always use bridge interface for ARP when VLAN is bridged
# Check: /interface bridge port print

## Invalid Firewall Rules:
/ip firewall filter remove [find invalid]

## Interface Not Responding:
/interface disable <interface-name>
:delay 2
/interface enable <interface-name>

## MAC Server Issues:
/tool mac-server set [find] disabled=no
/tool mac-server mac-winbox set [find] disabled=no

# ========================================
# TROUBLESHOOTING DECISION TREE
# ========================================

# 1. Can you ping the device?
#    NO → Check Layer 2 (MAC scan, ARP)
#    YES → Check management services

# 2. Does MAC scan show the device?
#    NO → Physical/VLAN configuration issue
#    YES → ARP resolution problem

# 3. Is ARP status "failed" or "incomplete"?
#    YES → Add static ARP entry
#    NO → Check firewall/services

# 4. Is interface bridged?
#    YES → Use bridge interface for ARP
#    NO → Use VLAN interface directly

# 5. Can you access web interface?
#    YES → Winbox/SSH service issue
#    NO → Device firewall blocking access

# ========================================
# END OF TROUBLESHOOTING GUIDE
# ========================================