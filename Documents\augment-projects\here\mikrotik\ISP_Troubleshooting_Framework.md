# ISP Troubleshooting Framework - MikroTik Edition

## 🎯 The RAPID Method for ISP Troubleshooting

**R** - Reproduce & Record  
**A** - Assess Impact & Scope  
**P** - Prioritize by Layer  
**I** - Isolate the Problem  
**D** - Deploy Solution & Document  

---

## 📋 Initial Information Gathering (First 2 Minutes)

### Customer Report Analysis
- **What exactly is broken?** (No internet, slow speeds, intermittent, buffering)
- **When did it start?** (Exact time helps correlate with changes)
- **How many users affected?** (Single user vs. widespread)
- **What changed recently?** (New equipment, config changes, weather)

### Quick Status Check Commands
```bash
# On MikroTik - Run these FIRST
/system resource print
/interface print stats
/ip route print where active
/log print where topics~"error|critical"
```

---

## 🔍 The 5-Layer Diagnostic Approach

### Layer 1: Physical Connectivity
**Time Investment: 30 seconds - 2 minutes**

```bash
# Check interface status
/interface print stats-detail
/interface ethernet print status

# Look for these RED FLAGS:
# - link=no (cable/fiber issue)
# - rx-error, tx-error increasing
# - rx-drop, tx-drop increasing
```

**Physical Checklist:**
- [ ] All cables properly seated
- [ ] SFP modules properly inserted
- [ ] Power levels on fiber links (-3dBm to -25dBm typical)
- [ ] LED status on switches/routers
- [ ] Environmental factors (heat, moisture)

### Layer 2: Data Link & Switching
**Time Investment: 1-3 minutes**

```bash
# VLAN and switching diagnostics
/interface vlan print
/interface bridge print detail
/interface bridge port print

# Check for:
# - VLAN mismatches
# - Bridge loops
# - MAC table issues
```

### Layer 3: Network & Routing
**Time Investment: 2-5 minutes**

```bash
# Routing table analysis
/ip route print where active
/ip route print where invalid

# Gateway connectivity
/ping [upstream-gateway] count=10
/traceroute [upstream-gateway]

# DNS resolution
/ping ******* count=5
/ping google.com count=5
```

### Layer 4-7: Services & Applications
**Time Investment: 1-2 minutes**

```bash
# Service status
/ip service print
/ip firewall connection print count-only

# Bandwidth utilization
/interface monitor-traffic [interface-name] duration=10
```

---

## 🚨 Critical Issue Identification Matrix

| Symptom | Likely Layer | First Action |
|---------|--------------|--------------|
| Complete outage | L1/L3 | Check physical + ping gateway |
| Slow speeds | L1/L2 | Check interface errors + utilization |
| Intermittent drops | L1/L2 | Monitor interface stats over time |
| Can't browse but ping works | L7 | Check DNS + firewall rules |
| Some sites work, others don't | L3/L7 | Check routing + MTU |

---

## ⚡ Quick Win Commands (30-Second Fixes)

```bash
# Reset interface (often fixes negotiation issues)
/interface ethernet set [interface] disabled=yes
/interface ethernet set [interface] disabled=no

# Clear ARP cache
/ip arp remove [ip-address]

# Restart DHCP client (for upstream issues)
/ip dhcp-client release [interface]
/ip dhcp-client renew [interface]

# Check and clear DNS cache
/ip dns cache flush
```

---

## 📊 Performance Baseline Commands

### Establish Normal Operations
```bash
# Bandwidth test to upstream
/tool bandwidth-test [upstream-ip] protocol=tcp direction=both duration=30s

# Latency baseline
/tool flood-ping [upstream-gateway] count=100 size=64

# Interface utilization over time
/tool graphing interface add interface=[wan-interface] store-on-disk=yes
```

---

## 🔧 Upstream Provider Issues

### Identifying Provider Problems
1. **Multiple customer reports** at same time
2. **Traceroute stops** at provider's equipment
3. **High latency/loss** to provider gateway
4. **BGP session down** (if applicable)

### Provider Escalation Checklist
- [ ] Circuit ID ready
- [ ] Exact error symptoms documented
- [ ] Traceroute results captured
- [ ] Interface statistics exported
- [ ] Timeline of issue established

---

## 📈 Trending and Monitoring

### Key Metrics to Track
- Interface utilization (>80% = congestion)
- Error rates (>0.1% = investigate)
- Latency to upstream (baseline + 50ms = issue)
- DNS response times
- Customer connection counts

### Automated Monitoring Setup
```bash
# Enable SNMP for monitoring tools
/snmp set enabled=yes contact="<EMAIL>"

# Log important events
/system logging add topics=error,critical,warning action=memory

# Email alerts for critical issues
/tool e-mail set server=[smtp-server] from=[your-email]
```

This framework gives you a systematic approach to quickly identify and resolve network issues. Each step builds on the previous one, ensuring you don't waste time on complex diagnostics when the issue might be simple.

**Next Steps:** Practice this methodology on known-good systems to build muscle memory, then apply it during actual outages.
