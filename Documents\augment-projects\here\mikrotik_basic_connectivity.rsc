# MikroTik Basic Connectivity Troubleshooting <PERSON>ript
# Run these commands step by step to diagnose and fix connectivity issues

# 1. Check current interface status
/interface print
/interface bridge print
/interface bridge port print

# 2. Check IP addresses
/ip address print

# 3. Check DHCP client status on WAN
/ip dhcp-client print

# 4. Check current routes
/ip route print

# 5. Check DNS settings
/ip dns print

# 6. Test basic connectivity from router itself
/ping ******* count=3
/ping google.com count=3

# 7. If above fails, try these fixes:

# Remove the VPN default route temporarily
/ip route remove [find gateway=avitechcloud]

# Add proper default route through WAN interface (adjust if needed)
# First check what gateway DHCP client received:
/ip dhcp-client print detail

# Then add default route (replace X.X.X.X with your actual gateway)
# /ip route add dst-address=0.0.0.0/0 gateway=X.X.X.X

# 8. Check firewall rules - they might be blocking traffic
/ip firewall filter print

# 9. Temporarily disable restrictive firewall rules
/ip firewall filter disable [find chain=forward src-address-list=skysync-guest]
/ip firewall filter disable [find chain=forward src-address-list=skysync-hs-guest]
/ip firewall filter disable [find chain=forward src-address-list=skysync-rogue]

# 10. Test connectivity again
/ping ******* count=3

# 11. Check NAT rules
/ip firewall nat print

# 12. If still no connectivity, check if WAN interface is getting IP
/interface print stats