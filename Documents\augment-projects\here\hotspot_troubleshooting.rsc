# Hotspot Troubleshooting Commands

# 1. Check hotspot status and active users
/ip hotspot print
/ip hotspot active print

# 2. Check if user is authenticated
/ip hotspot cookie print

# 3. Check DHCP leases
/ip dhcp-server lease print

# 4. Check firewall rules order
/ip firewall filter print

# 5. Check NAT rules
/ip firewall nat print where chain=srcnat

# 6. Test connectivity from router
/ping ******* count=3

# 7. Check VPN status
/interface ovpn-client print detail

# 8. Check routes
/ip route print

# 9. Check logs for errors
/log print where topics~"hotspot"
/log print where topics~"dhcp"

# 10. Check walled garden
/ip hotspot walled-garden print
/ip hotspot walled-garden ip print

# Common issues and fixes:

# If not redirected to login page:
# - Check hotspot is enabled: /ip hotspot print
# - Check client gets IP: /ip dhcp-server lease print
# - Check HTTP redirect rules in NAT

# If authenticated but no internet:
# - Check user profile: /ip hotspot user profile print
# - Check firewall rules allow traffic
# - Check NAT masquerade rules
# - Check VPN connectivity

# If login page doesn't work:
# - Check walled garden allows login server
# - Check DNS resolution works
# - Check RADIUS server connectivity