# RSTP Single Bridge with VLANs - Complete Tutorial
# Progressive learning approach for Layer 2 failover

# ============================================================================
# STEP 1: Basic RSTP Bridge Setup
# ============================================================================

# Create the main bridge with RSTP enabled
/interface bridge
add name=bridge-main protocol-mode=rstp priority=0x1000 max-message-age=6s hello-time=1s forward-delay=4s

# Why these settings?
# - protocol-mode=rstp: Enables Rapid Spanning Tree
# - priority=0x1000: Lower = higher priority (0x0000 = highest, 0xFFFF = lowest)
# - hello-time=1s: How often BPDU messages are sent
# - forward-delay=4s: Time spent in Learning state
# - max-message-age=6s: How long to keep BPDU info

# ============================================================================
# STEP 2: Add Physical Interfaces with Strategic Priorities
# ============================================================================

# Add interfaces to bridge with different priorities for load balancing
/interface bridge port
add bridge=bridge-main interface=ether1 priority=0x10 path-cost=10    # Primary path (lowest cost)
add bridge=bridge-main interface=ether2 priority=0x20 path-cost=100   # Secondary path
add bridge=bridge-main interface=ether3 priority=0x30 path-cost=200   # Tertiary path
add bridge=bridge-main interface=ether4 priority=0x40 path-cost=300   # Last resort

# Priority Logic:
# - Lower priority number = higher priority
# - 0x10 (16) > 0x20 (32) > 0x30 (48) > 0x40 (64)
# - Path cost determines best path to root bridge
# - Lower path cost = preferred path

# ============================================================================
# STEP 3: Create VLANs on the Bridge
# ============================================================================

# Create VLAN interfaces
/interface vlan
add interface=bridge-main name=vlan10-users vlan-id=10
add interface=bridge-main name=vlan20-servers vlan-id=20  
add interface=bridge-main name=vlan30-guest vlan-id=30
add interface=bridge-main name=vlan99-management vlan-id=99

# Assign IP addresses to VLANs for testing
/ip address
add address=************/24 interface=vlan10-users
add address=************/24 interface=vlan20-servers
add address=************/24 interface=vlan30-guest
add address=************/24 interface=vlan99-management

# ============================================================================
# STEP 4: Enable VLAN Filtering (Advanced)
# ============================================================================

# Enable VLAN filtering for proper VLAN isolation
/interface bridge
set bridge-main vlan-filtering=yes

# Configure VLAN membership table
/interface bridge vlan
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=10
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=20
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=30
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=99

# Configure access ports (untagged) if needed
# add bridge=bridge-main untagged=ether5 vlan-ids=10  # ether5 = VLAN 10 access port

# ============================================================================
# STEP 5: RSTP Optimization for VLANs
# ============================================================================

# Configure edge ports (ports connected to end devices, not switches)
/interface bridge port
set [find interface=ether5] edge=yes           # End device - no STP needed
set [find interface=ether6] edge=yes           # End device - no STP needed

# Configure point-to-point links (direct switch-to-switch connections)
/interface bridge port  
set [find interface=ether1] point-to-point=yes # Direct to another switch
set [find interface=ether2] point-to-point=yes # Direct to another switch

# Why these optimizations?
# - edge=yes: Immediately transitions to forwarding (no STP delay)
# - point-to-point=yes: Enables rapid transition using proposal/agreement

# ============================================================================
# STEP 6: Load Balancing Strategy
# ============================================================================

# Method 1: Different priorities per port (what we did above)
# - VLAN traffic will prefer lower-priority ports
# - All VLANs follow same path preference

# Method 2: Alternative - Multiple bridges for VLAN groups (advanced)
# This would be for complex scenarios - we'll stick with single bridge

# ============================================================================
# STEP 7: Monitoring and Verification Commands
# ============================================================================

# Essential monitoring commands (run these to verify setup):

# 1. Check bridge status
# /interface bridge print detail

# 2. View port states and roles
# /interface bridge port print detail

# 3. Monitor specific port STP state
# /interface bridge port monitor [find interface=ether1]

# 4. Check VLAN table
# /interface bridge vlan print

# 5. View bridge statistics
# /interface bridge print stats

# 6. Check for topology changes
# /log print where topics~"bridge"

# ============================================================================
# STEP 8: Failover Testing Scenarios
# ============================================================================

# Test Scenario 1: Primary link failure
# - Disconnect ether1 (primary path)
# - Observe: ether2 should become forwarding within 1-6 seconds
# - Command: /interface bridge port monitor [find interface=ether2]

# Test Scenario 2: Root bridge failure  
# - If this device is root, change priority to higher value
# - /interface bridge set bridge-main priority=0x8000
# - Observe: New root election and path recalculation

# Test Scenario 3: VLAN connectivity test
# - Ping between VLAN interfaces during failover
# - /ping ************ interface=vlan10-users

# ============================================================================
# STEP 9: Troubleshooting Common Issues
# ============================================================================

# Issue 1: Slow convergence
# Solution: Verify point-to-point and edge port settings

# Issue 2: Loops detected
# Solution: Check for duplicate bridge priorities or misconfigured costs

# Issue 3: VLANs not working
# Solution: Verify VLAN filtering and membership table

# Issue 4: Frequent topology changes
# Solution: Check physical connections and cable quality

# ============================================================================
# STEP 10: Production Best Practices
# ============================================================================

# 1. Always set bridge priority manually (don't rely on MAC address)
# 2. Use consistent path costs across your network
# 3. Configure edge ports for end devices
# 4. Monitor bridge logs regularly
# 5. Test failover scenarios before production
# 6. Document your STP topology and priorities
# 7. Use management VLAN for out-of-band access

# ============================================================================
# NEXT STEPS FOR LEARNING
# ============================================================================

# 1. Practice with this basic setup
# 2. Add more switches to create redundant topology
# 3. Experiment with different priority values
# 4. Test failover scenarios
# 5. Monitor convergence times
# 6. Learn about MSTP for advanced scenarios
