# Updated configuration for /22 subnet

# STEP 3: ASSIGN IP ADDRESS TO BRIDGE (UPDATED)
/ip address
add address=*********/22 interface=bridge_hotspot network=********* comment="Hotspot gateway IP"

# STEP 6: CREATE IP POOLS (UPDATED)
/ip pool
add name=hs-pool-6 ranges=**********-*********** comment="IP pool for hotspot users"

# STEP 7: CONFIGURE DHCP SERVER NETWORK (UPDATED)
/ip dhcp-server network
add address=*********/22 comment="hotspot network" gateway=********* dns-server=*******,*******

# STEP 15: FIREWALL RULE FOR HOTSPOT LOGIN (UPDATED)
/ip firewall filter
add action=accept chain=input dst-port=80,8080 protocol=tcp src-address=*********/22 comment="Allow hotspot login"