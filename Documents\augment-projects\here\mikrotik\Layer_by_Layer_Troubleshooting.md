# Layer-by-Layer Network Troubleshooting - ISP Edition

## 🎯 The Bottom-Up Approach

**Rule #1:** Always start at the physical layer and work up  
**Rule #2:** Don't skip layers - each one builds on the previous  
**Rule #3:** Fix one layer completely before moving to the next  

---

## 🔌 Layer 1: Physical Layer (30 seconds - 2 minutes)

### What You're Looking For
- Cable connectivity issues
- Port failures
- Power problems
- Environmental issues

### MikroTik Commands
```bash
# Quick physical status check
/interface print status
/interface ethernet print status

# Detailed interface statistics
/interface print stats-detail where name~"ether|sfp"

# SFP module diagnostics (fiber)
/interface sfp print detail
/interface sfp monitor [sfp-interface] duration=10
```

### Red Flags to Watch For
- **link=no** → Cable/fiber disconnected
- **rx-error or tx-error increasing** → Bad cable/port
- **rx-drop or tx-drop increasing** → Overutilization or hardware issue
- **SFP power levels outside -3dBm to -25dBm** → Fiber issue

### Quick Fixes
```bash
# Reset interface (fixes 60% of physical issues)
/interface ethernet set [interface] disabled=yes
/interface ethernet set [interface] disabled=no

# Force speed/duplex if auto-negotiation fails
/interface ethernet set [interface] speed=1Gbps duplex=full auto-negotiation=no
```

### Physical Checklist
- [ ] All cables firmly seated
- [ ] No visible damage to cables
- [ ] SFP modules properly inserted
- [ ] Power LEDs green on all equipment
- [ ] No overheating (check fans)

---

## 🔗 Layer 2: Data Link Layer (1-3 minutes)

### What You're Looking For
- VLAN misconfigurations
- Bridge loops
- MAC address conflicts
- Switching issues

### MikroTik Commands
```bash
# VLAN configuration check
/interface vlan print detail
/interface bridge vlan print

# Bridge status and MAC table
/interface bridge print detail
/interface bridge host print

# Check for bridge loops
/interface bridge port print where edge=no
/log print where message~"loop"
```

### Common Layer 2 Issues

#### VLAN Problems
```bash
# Verify VLAN tags match upstream
/interface vlan print where vlan-id=[expected-vlan]

# Check bridge VLAN filtering
/interface bridge print detail where vlan-filtering=yes
```

#### Bridge Loops
```bash
# Enable STP if not already
/interface bridge set [bridge] protocol-mode=stp

# Check for duplicate MAC addresses
/interface bridge host print where !dynamic
```

### Layer 2 Fixes
```bash
# Clear bridge MAC table
/interface bridge host remove [mac-address]

# Reset bridge port
/interface bridge port set [port] disabled=yes
/interface bridge port set [port] disabled=no

# Fix VLAN mismatch
/interface vlan set [vlan-interface] vlan-id=[correct-vlan]
```

---

## 🌐 Layer 3: Network Layer (2-5 minutes)

### What You're Looking For
- Routing issues
- IP address conflicts
- Gateway problems
- Subnet misconfigurations

### MikroTik Commands
```bash
# Routing table analysis
/ip route print where active
/ip route print where invalid

# Gateway connectivity
/ping [default-gateway] count=10
/traceroute [upstream-gateway]

# IP configuration check
/ip address print detail
/ip route print where gateway=[gateway-ip]
```

### Critical Layer 3 Tests

#### Gateway Reachability
```bash
# Test default gateway
/ping [gateway-ip] count=10 size=64

# If gateway fails, check ARP
/ip arp print where address=[gateway-ip]

# Clear ARP if stale
/ip arp remove [gateway-ip]
```

#### Routing Issues
```bash
# Check for conflicting routes
/ip route print where dst-address="0.0.0.0/0"

# Verify route metrics
/ip route print detail where active

# Test specific destination
/traceroute [problem-destination]
```

### Layer 3 Fixes
```bash
# Add missing default route
/ip route add dst-address=0.0.0.0/0 gateway=[gateway-ip]

# Fix IP address conflict
/ip address set [address-entry] address=[new-ip/mask]

# Refresh DHCP lease
/ip dhcp-client renew [interface]
```

---

## 🚪 Layer 4: Transport Layer (1-2 minutes)

### What You're Looking For
- Port blocking
- Connection limits
- TCP/UDP issues
- Firewall problems

### MikroTik Commands
```bash
# Check active connections
/ip firewall connection print count-only
/ip firewall connection print where protocol=tcp

# Connection states
/ip firewall connection print where connection-state=established

# Port-specific connections
/ip firewall connection print where dst-port=80
```

### Common Layer 4 Issues

#### Connection Limits
```bash
# Check connection tracking table size
/ip firewall connection tracking print

# Monitor connection count over time
/ip firewall connection print count-only
```

#### Firewall Blocking
```bash
# Check filter rules with stats
/ip firewall filter print stats

# Look for DROP rules with high packet counts
/ip firewall filter print stats where action=drop and packets>0
```

### Layer 4 Fixes
```bash
# Increase connection tracking
/ip firewall connection tracking set tcp-established-timeout=1d

# Temporarily disable firewall (CAREFUL!)
/ip firewall filter disable [rule-number]

# Clear connection tracking
/ip firewall connection remove [connection-id]
```

---

## 🌍 Layer 5-7: Session/Presentation/Application (1-3 minutes)

### What You're Looking For
- DNS resolution issues
- Application-specific problems
- SSL/TLS issues
- Service availability

### MikroTik Commands
```bash
# DNS functionality
/ping google.com count=3
/tool nslookup google.com [dns-server]

# Service status
/ip service print

# HTTP/HTTPS testing
/tool fetch url="http://google.com" mode=http
```

### Application Layer Tests

#### DNS Resolution
```bash
# Test DNS servers
/ping ******* count=3
/ping ******* count=3

# Check DNS cache
/ip dns cache print where name~"google"

# Clear DNS cache
/ip dns cache flush
```

#### Web Connectivity
```bash
# Test HTTP connectivity
/tool fetch url="http://www.google.com" mode=http

# Test HTTPS
/tool fetch url="https://www.google.com" mode=https

# Check specific ports
/tool telnet [target-ip] [port]
```

---

## 🔄 The Systematic Approach

### Step 1: Gather Information (30 seconds)
```bash
# Quick system overview
/system resource print
/interface print stats
/ip route print where active
```

### Step 2: Test Each Layer (Bottom-Up)
1. **Physical**: Check link status and errors
2. **Data Link**: Verify VLANs and switching
3. **Network**: Test routing and IP connectivity
4. **Transport**: Check ports and connections
5. **Application**: Test services and DNS

### Step 3: Isolate the Problem
- **If Layer 1 fails**: Fix physical issues first
- **If Layer 2 fails**: Check VLANs and switching
- **If Layer 3 fails**: Focus on routing/IP
- **If Layer 4+ fails**: Look at services/firewall

### Step 4: Verify the Fix
```bash
# Re-run the failing test
# Check all layers above the fixed layer
# Monitor for 5-10 minutes to ensure stability
```

---

## 🎯 Quick Decision Tree

```
Internet Issue Reported
├── Can ping gateway? 
│   ├── NO → Check Layers 1-3
│   └── YES → Check Layers 4-7
├── Can resolve DNS?
│   ├── NO → DNS/Layer 7 issue
│   └── YES → Application/Service issue
├── Can reach external IPs?
│   ├── NO → Routing/Layer 3 issue
│   └── YES → Firewall/Layer 4 issue
```

---

## 💡 Pro Tips

1. **Always baseline first** - Know what "normal" looks like
2. **One change at a time** - Don't make multiple changes simultaneously
3. **Document everything** - Keep notes of what you tried
4. **Test after each fix** - Verify the issue is resolved
5. **Monitor stability** - Watch for 10+ minutes after fixing

This systematic approach ensures you find the root cause quickly without wasting time on unnecessary diagnostics.
