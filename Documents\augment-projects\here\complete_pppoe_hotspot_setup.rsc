# ========================================
# COMPLETE PPPOE + HOTSPOT SETUP SCRIPT
# ========================================

# STEP 1: CREATE BRIDGE FOR LAN INTERFACES
# ========================================
# Create a bridge to combine multiple ethernet ports
/interface bridge
add name=bridge_hotspot comment="Bridge for hotspot and PPPoE clients"

# Add ethernet ports 2-5 to the bridge (port 1 is WAN)
/interface bridge port
add bridge=bridge_hotspot interface=ether2 comment="LAN port 2"
add bridge=bridge_hotspot interface=ether3 comment="LAN port 3" 
add bridge=bridge_hotspot interface=ether4 comment="LAN port 4"
add bridge=bridge_hotspot interface=ether5 comment="LAN port 5"

# STEP 2: CONFIGURE WIRELESS ACCESS POINT
# =======================================
# Set wireless security to open (no WiFi password)
/interface wireless security-profiles
set [ find default=yes ] authentication-types="" mode=none

# Configure wireless as access point
/interface wireless
set wlan1 mode=ap-bridge ssid=SkySyncHotspot security-profile=default disabled=no frequency=2437 band=2ghz-b/g/n

# Add wireless to bridge
/interface bridge port
add bridge=bridge_hotspot interface=wlan1 comment="Wireless interface"

# STEP 3: ASSIGN IP ADDRESS TO BRIDGE
# ===================================
# Give the bridge an IP address (this becomes the gateway)
/ip address
add address=*********/21 interface=bridge_hotspot network=********* comment="Hotspot gateway IP"

# STEP 4: CONFIGURE WAN CONNECTION
# ================================
# Set up DHCP client on WAN interface to get internet
/ip dhcp-client
add interface=ether1 disabled=no comment="WAN internet connection"

# Configure DNS servers
/ip dns
set allow-remote-requests=yes servers=*******,*******,*******,*******

# STEP 5: SETUP NAT FOR INTERNET ACCESS
# =====================================
# Allow clients to access internet through WAN
/ip firewall nat
add action=masquerade chain=srcnat out-interface=ether1 comment="NAT for internet access"

# STEP 6: CREATE IP POOLS
# =======================
# Pool for hotspot users
/ip pool
add name=hs-pool-6 ranges=**********-*********** comment="IP pool for hotspot users"

# Pool for PPPoE users  
/ip pool
add name=PPPOE_POOL ranges=************-************* comment="IP pool for PPPoE users"

# Pool for guest users
/ip pool
add name=GUEST_POOL ranges=************/23 comment="IP pool for guest users"

# STEP 7: CONFIGURE DHCP SERVER
# =============================
# Create DHCP server for hotspot users
/ip dhcp-server
add address-pool=hs-pool-6 interface=bridge_hotspot name=dhcp1 lease-time=1d comment="DHCP for hotspot"

# Configure DHCP network settings
/ip dhcp-server network
add address=*********/21 comment="hotspot network" gateway=********* dns-server=*******,*******

# STEP 8: CREATE HOTSPOT PROFILE
# ==============================
# Create hotspot profile (disabled RADIUS for simplicity)
/ip hotspot profile
add hotspot-address=********* name=hsprof1 use-radius=no login-by=cookie,http-chap,http-pap,mac-cookie comment="Main hotspot profile"

# STEP 9: CREATE HOTSPOT SERVER
# =============================
# Create the actual hotspot server
/ip hotspot
add address-pool=hs-pool-6 disabled=no interface=bridge_hotspot name=skysync profile=hsprof1 comment="Main hotspot server"

# STEP 10: CREATE USER PROFILES FOR DIFFERENT ACCESS LEVELS
# =========================================================
# Default profile (full access)
/ip hotspot user profile
set [ find default=yes ] idle-timeout=5m keepalive-timeout=30s shared-users=5 comment="Default full access"

# Guest profile (limited access)
add name=skysyncguest idle-timeout=2m rate-limit=512k/512k shared-users=5 transparent-proxy=yes comment="Guest limited access"

# Quarantine profile (very limited)
add name=quarantine idle-timeout=2m shared-users=5 comment="Quarantine profile"

# STEP 11: CREATE HOTSPOT USERS
# =============================
# Create test users for hotspot
/ip hotspot user
add name=admin password=admin123 profile=default comment="Admin user"
add name=guest password=guest123 profile=skysyncguest comment="Guest user"
add name=test password=test profile=default comment="Test user"

# STEP 12: CREATE PPPOE PROFILES
# ==============================
# Default PPPoE profile
/ppp profile
add dns-server=*******,******* local-address=PPPOE_POOL name=skysyncdefault remote-address=PPPOE_POOL comment="Default PPPoE profile"

# Guest PPPoE profile with bandwidth limit
/ppp profile
add dns-server=*******,******* local-address=GUEST_POOL name=skysyncguest remote-address=GUEST_POOL rate-limit=512k/512k comment="Guest PPPoE profile"

# STEP 13: CREATE PPPOE SERVER
# ============================
# Create PPPoE server on the bridge
/interface pppoe-server server
add authentication=pap default-profile=skysyncdefault disabled=no interface=bridge_hotspot max-mru=1500 max-mtu=1500 service-name=skysyncpppoe comment="PPPoE server"

# STEP 14: CREATE PPPOE USERS
# ===========================
# Create PPPoE users (add more as needed)
/ppp secret
add name=pppoe_user1 password=pass123 profile=skysyncdefault service=pppoe comment="PPPoE user 1"
add name=pppoe_guest password=guest123 profile=skysyncguest service=pppoe comment="PPPoE guest user"

# STEP 15: CONFIGURE FIREWALL (OPTIONAL SECURITY)
# ===============================================
# Allow established and related connections
/ip firewall filter
add action=accept chain=forward connection-state=established,related comment="Allow established connections"

# Allow traffic from LAN to WAN
/ip firewall filter
add action=accept chain=forward in-interface=bridge_hotspot out-interface=ether1 comment="Allow LAN to WAN"

# STEP 16: WALLED GARDEN (OPTIONAL)
# =================================
# Allow access to specific sites without login
/ip hotspot walled-garden
add dst-host=google.com comment="Allow Google without login"
add dst-host=facebook.com comment="Allow Facebook without login"

# ========================================
# VERIFICATION COMMANDS (RUN MANUALLY)
# ========================================
# Check if everything is working:
# /interface bridge print
# /ip hotspot print  
# /ip hotspot active print
# /interface pppoe-server server print
# /ppp active print
# /ip dhcp-server lease print
# /ping ******* count=3

# ========================================
# HOW TO USE:
# ========================================
# HOTSPOT USERS:
# 1. Connect to "SkySyncHotspot" WiFi (no password)
# 2. Open browser, go to any website
# 3. Login with: admin/admin123 or guest/guest123 or test/test
#
# PPPOE USERS:
# 1. Configure PPPoE client on device
# 2. Use credentials: pppoe_user1/pass123 or pppoe_guest/guest123
# 3. Service name: skysyncpppoe