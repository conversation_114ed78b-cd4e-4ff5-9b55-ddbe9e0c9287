# VPN Connectivity Troubleshooting for MikroTik

# 1. Check OpenVPN client status
/interface ovpn-client print detail

# 2. Check if VPN interface is up and has IP
/ip address print where interface=avitechcloud

# 3. Check VPN connection logs
/log print where topics~"ovpn"

# 4. Test basic WAN connectivity first (before VPN)
/ping *************** count=3 interface=ether1

# 5. Check if certificate is properly imported
/certificate print

# 6. If VPN is down, try these fixes:

# Disable and re-enable VPN client
/interface ovpn-client disable avitechcloud
:delay 2s
/interface ovpn-client enable avitechcloud

# 7. Add backup route through WAN for VPN server
/ip route add dst-address=***************/32 gateway=[/ip dhcp-client get [find interface=ether1] gateway]

# 8. Fix NAT rule to include VPN interface
/ip firewall nat remove [find out-interface=ether1]
/ip firewall nat add action=masquerade chain=srcnat out-interface-list=WAN

# Create interface list for WAN interfaces
/interface list add name=WAN
/interface list member add interface=ether1 list=WAN
/interface list member add interface=avitechcloud list=WAN

# 9. Test connectivity after VPN is up
/ping ******* count=3