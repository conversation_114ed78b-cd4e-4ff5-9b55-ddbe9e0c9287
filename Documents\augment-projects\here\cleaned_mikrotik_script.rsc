# Create bridge and add ports
/interface bridge
add name=bridge_hotspot

# Add ports to bridge
/interface bridge port
add bridge=bridge_hotspot interface=ether2
add bridge=bridge_hotspot interface=ether3
add bridge=bridge_hotspot interface=ether4
add bridge=bridge_hotspot interface=ether5

# Add Hotspot IP to the interface or bridge where hotspot will be running
/ip address
add address=*********/21 interface=bridge_hotspot network=*********

# Configure DNS server if not already set
/ip dns
set allow-remote-requests=yes servers=*******,*******,*******,*******

# Configure DHCP client on WAN interface (if needed)
/ip dhcp-client
add interface=ether1 disabled=no

# NAT all traffic leaving through the WAN interface
/ip firewall nat
add action=masquerade chain=srcnat out-interface=ether1

# Create hotspot profile
/ip hotspot profile
add hotspot-address=********* html-directory=hotspot http-cookie-lifetime=4w2d login-by=cookie,http-chap,http-pap,mac-cookie name=hsprof1 radius-interim-update=5m use-radius=yes

# Create 2 hotspot user profiles and edit the default
/ip hotspot user profile
set [ find default=yes ] idle-timeout=5m keepalive-timeout=30s mac-cookie-timeout=4w2d on-login="/ip firewall address-list remove [find comment=\"\$\"mac-address\"\"]" shared-users=5

add add-mac-cookie=no address-list=skysync-hs-guest idle-timeout=2m keepalive-timeout=15s !mac-cookie-timeout name=skysyncguest \
    on-login=":local username \$user;\r\n:log info \$username;\r\n/ip hotspot cookie remove [/ip hotspot cookie find where user=\"\$username\"];" \
    on-logout="/ip firewall address-list remove [find comment=\"\$\"mac-address\"\"]" rate-limit=512k/512k shared-users=5 transparent-proxy=yes

add add-mac-cookie=no address-list=skysync-rogue idle-timeout=2m !mac-cookie-timeout name=quarantine \
    on-login=":local username \$user;\r\n:log info \$username;\r\n/ip hotspot cookie remove [/ip hotspot cookie find where user=\"\$username\"];" \
    on-logout="/ip firewall address-list remove [find comment=\"\$\"mac-address\"\"]" shared-users=5

# Create IP pools for PPPoE and hotspot
/ip pool
add name=PPPOE_POOL ranges=************-*************
add name=GUEST_POOL ranges=************/23
add name=ROGUE_POOL ranges=************/23
add name=hs-pool-6 ranges=**********-***********

# Create DHCP server for hotspot
/ip dhcp-server
add address-pool=hs-pool-6 interface=bridge_hotspot name=dhcp1 lease-time=7305d

# Add the hotspot server
/ip hotspot
add address-pool=hs-pool-6 disabled=no interface=bridge_hotspot name=skysync profile=hsprof1

# Create PPPoE profiles
/ppp profile
add dns-server=*******,******* local-address=PPPOE_POOL name=skysyncdefault \
    on-up="/ip firewall address-list remove [find comment=\"\$\"mac-address\"]" remote-address=PPPOE_POOL

add address-list=skysync-guest dns-server=*******,******* local-address=GUEST_POOL name=skysyncguest \
    on-down="/ip firewall address-list remove [find comment=\"\$\"mac-address\"]" rate-limit=512k/512k remote-address=GUEST_POOL

add address-list=skysync-rogue dns-server=*******,******* local-address=ROGUE_POOL name=quarantine \
    on-down="/ip firewall address-list remove [find comment=\"\$\"mac-address\"]" rate-limit=256k/256k remote-address=ROGUE_POOL

# Create PPPoE server
/interface pppoe-server server
add authentication=pap default-profile=skysyncdefault disabled=no interface=bridge_hotspot max-mru=1500 max-mtu=1500 mrru=1640 service-name=skysyncpppoe

# Add DHCP server network
/ip dhcp-server network
add address=*********/21 comment="hotspot network" gateway=*********

# Whitelist private IP ranges
/ip firewall address-list
add address=***********/16 list=hotspot-whitelist
add address=**********/12 list=hotspot-whitelist
add address=10.0.0.0/8 list=hotspot-whitelist

# Firewall filter rules
/ip firewall filter
add action=passthrough chain=unused-hs-chain comment="place hotspot rules here" disabled=yes
add action=accept chain=forward src-address=***************
add action=accept chain=forward dst-address=***************
add action=reject chain=forward dst-address=!*************** reject-with=icmp-network-unreachable src-address-list=skysync-guest
add action=reject chain=forward dst-address=!*************** reject-with=icmp-network-unreachable src-address-list=skysync-hs-guest
add action=reject chain=forward dst-address=!*************** reject-with=icmp-network-unreachable src-address-list=skysync-rogue

# Walled garden host rules
/ip hotspot walled-garden
add dst-host=skysync.avitechcloud.com
add dst-host=vinea.fra1.digitaloceanspaces.com

# Walled garden IP rules
/ip hotspot walled-garden ip
add action=accept disabled=no dst-address-list=hotspot-whitelist src-address-list=hotspot-whitelist
add action=accept disabled=no dst-address=***************
add action=accept disabled=no src-address=***************

# Enable RADIUS for PPPoE
/ppp aaa
set interim-update=15m use-radius=yes

# RADIUS server settings
/radius
add address=********** secret=SolutionTechAvi service=ppp timeout=3s
add address=********** secret=SolutionTechAvi service=hotspot timeout=6s

# Allow RADIUS updates
/radius incoming
set accept=yes

# OVPN client setup
/interface ovpn-client
add certificate=skysyncofficetest1.ovpn_1 cipher=aes128-cbc connect-to=*************** name=avitechcloud password=AviSoll profile=default-encryption use-peer-dns=no user=skysync

# Default route through VPN
/ip route
add dst-address=0.0.0.0/0 gateway=avitechcloud

# Verification commands (run these manually to check status)
# /interface bridge print
# /interface bridge port print
# /ip address print
# /ip hotspot print
# /ip dhcp-server print
# /interface ovpn-client print
# /ping ******* count=3
# /ping *************** count=3