# VLAN Failover Configuration Examples for MikroTik
# Best practices for Layer 2 redundancy with VLANs

# ============================================================================
# SCENARIO 1: RSTP with VLAN Trunks (RECOMMENDED for most cases)
# ============================================================================

# Create bridge with RSTP enabled
/interface bridge
add name=bridge-main protocol-mode=rstp

# Configure bridge ports with different priorities for load balancing
/interface bridge port
add bridge=bridge-main interface=ether1 priority=0x10
add bridge=bridge-main interface=ether2 priority=0x20
add bridge=bridge-main interface=ether3 priority=0x30

# Create VLANs on the bridge
/interface vlan
add interface=bridge-main name=vlan10-users vlan-id=10
add interface=bridge-main name=vlan20-servers vlan-id=20
add interface=bridge-main name=vlan30-guest vlan-id=30

# Configure RSTP settings for optimal VLAN performance
/interface bridge
set bridge-main priority=0x1000 max-message-age=6s hello-time=1s forward-delay=4s

# Enable VLAN filtering on bridge
/interface bridge
set bridge-main vlan-filtering=yes

# Configure VLAN table for proper trunk operation
/interface bridge vlan
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=10
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=20
add bridge=bridge-main tagged=ether1,ether2,ether3,bridge-main vlan-ids=30

# ============================================================================
# SCENARIO 2: Link Aggregation (LACP) for VLAN Trunks
# ============================================================================

# Create bonding interface for aggregated trunk
/interface bonding
add mode=802.3ad name=bond-trunk slaves=ether1,ether2 transmit-hash-policy=layer-2-and-3

# Create bridge on bonding interface
/interface bridge
add name=bridge-bonded protocol-mode=rstp

/interface bridge port
add bridge=bridge-bonded interface=bond-trunk

# Create VLANs on bonded bridge
/interface vlan
add interface=bridge-bonded name=vlan10-bond vlan-id=10
add interface=vlan20-bond name=vlan20-bond vlan-id=20

# ============================================================================
# SCENARIO 3: Redundant Uplinks with VLAN Failover
# ============================================================================

# Primary and backup uplink configuration
/interface bridge
add name=bridge-uplink protocol-mode=rstp

# Configure uplink ports with different costs
/interface bridge port
add bridge=bridge-uplink interface=ether1 path-cost=10    # Primary (lower cost)
add bridge=bridge-uplink interface=ether2 path-cost=100   # Backup (higher cost)

# VLAN configuration for uplinks
/interface vlan
add interface=bridge-uplink name=vlan100-wan vlan-id=100

# ============================================================================
# MONITORING AND FAILOVER SCRIPTS
# ============================================================================

# Script to monitor VLAN connectivity and trigger failover
/system script
add name=vlan-failover-monitor source={
    # Check primary VLAN gateway
    :local primaryGW "************"
    :local backupGW "************"
    
    # Ping test
    :local pingResult [/ping $primaryGW count=3]
    
    # If primary fails, adjust bridge port priority
    :if ($pingResult = 0) do={
        /interface bridge port set [find interface=ether1] priority=0x80
        /interface bridge port set [find interface=ether2] priority=0x10
        :log warning "VLAN Failover: Switched to backup path"
    } else={
        /interface bridge port set [find interface=ether1] priority=0x10
        /interface bridge port set [find interface=ether2] priority=0x80
        :log info "VLAN Failover: Primary path restored"
    }
}

# Schedule the monitoring script
/system scheduler
add interval=30s name=vlan-monitor on-event=vlan-failover-monitor

# ============================================================================
# VLAN-AWARE FAILOVER WITH DIFFERENT PATHS PER VLAN
# ============================================================================

# Create separate bridges for different VLAN groups
/interface bridge
add name=bridge-vlan10-20 protocol-mode=rstp priority=0x1000
add name=bridge-vlan30-40 protocol-mode=rstp priority=0x2000

# Distribute VLANs across different physical paths
/interface bridge port
add bridge=bridge-vlan10-20 interface=ether1 priority=0x10  # VLANs 10,20 prefer ether1
add bridge=bridge-vlan10-20 interface=ether2 priority=0x80

add bridge=bridge-vlan30-40 interface=ether1 priority=0x80  # VLANs 30,40 prefer ether2  
add bridge=bridge-vlan30-40 interface=ether2 priority=0x10

# ============================================================================
# BEST PRACTICES SUMMARY
# ============================================================================

# 1. Use RSTP instead of STP for faster convergence (1-6s vs 30-50s)
# 2. Configure different bridge priorities for load balancing
# 3. Use LACP bonding for high-bandwidth VLAN trunks
# 4. Monitor VLAN connectivity with scripts
# 5. Consider VLAN-aware failover for complex topologies
# 6. Always test failover scenarios before production deployment

# ============================================================================
# VERIFICATION COMMANDS
# ============================================================================

# Check bridge status
# /interface bridge print
# /interface bridge port print

# Monitor STP state
# /interface bridge port monitor 0

# Check VLAN table
# /interface bridge vlan print

# View bonding status
# /interface bonding monitor 0
