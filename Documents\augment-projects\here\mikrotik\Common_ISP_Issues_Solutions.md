# Common ISP Issues & Solutions - Real-World Scenarios

## 🚨 Scenario 1: "Complete Internet Outage"

### Customer Report
*"No internet at all, can't access anything"*

### Rapid Diagnosis (2 minutes)
```bash
# Step 1: Check physical connectivity
/interface print status where name~"wan|uplink"

# Step 2: Test gateway connectivity  
/ping [upstream-gateway] count=5

# Step 3: Check routing
/ip route print where active and dst-address="0.0.0.0/0"
```

### Common Causes & Solutions

#### Cause 1: Upstream Link Down
**Symptoms:** WAN interface shows `link=no`
```bash
# Check interface status
/interface ethernet print status where name=[wan-interface]

# If link is down, check:
# - Cable connections
# - SFP module (if fiber)
# - Upstream provider status
```

#### Cause 2: DHCP Lease Expired
**Symptoms:** No IP address on WAN interface
```bash
# Check DHCP client status
/ip dhcp-client print detail

# Solution: Renew lease
/ip dhcp-client renew [wan-interface]
```

#### Cause 3: Routing Table Corruption
**Symptoms:** No default route or invalid routes
```bash
# Check for missing default route
/ip route print where dst-address="0.0.0.0/0"

# Add default route if missing
/ip route add dst-address=0.0.0.0/0 gateway=[upstream-gateway]
```

---

## 🐌 Scenario 2: "Internet is Very Slow"

### Customer Report
*"Internet is working but extremely slow, pages take forever to load"*

### Rapid Diagnosis (3 minutes)
```bash
# Step 1: Check interface utilization
/interface monitor-traffic [wan-interface] duration=30

# Step 2: Test bandwidth to upstream
/tool bandwidth-test [upstream-gateway] protocol=tcp direction=both duration=20s

# Step 3: Check for errors
/interface print stats where rx-error>0 or tx-error>0
```

### Common Causes & Solutions

#### Cause 1: Interface Errors/Drops
**Symptoms:** High error or drop counts
```bash
# Check for errors
/interface print stats-detail where name=[wan-interface]

# Solution: Reset interface
/interface ethernet set [wan-interface] disabled=yes
/interface ethernet set [wan-interface] disabled=no

# If errors persist, check cables/SFP
```

#### Cause 2: Duplex Mismatch
**Symptoms:** High collision count, slow speeds
```bash
# Check auto-negotiation
/interface ethernet print detail where name=[wan-interface]

# Force full duplex
/interface ethernet set [wan-interface] duplex=full auto-negotiation=no
```

#### Cause 3: DNS Issues
**Symptoms:** Slow page loading, fast ping times
```bash
# Test DNS response time
/tool nslookup google.com *******

# Solution: Change DNS servers
/ip dns set servers=*******,*******
/ip dns cache flush
```

#### Cause 4: QoS/Traffic Shaping
**Symptoms:** Consistent speed limitation
```bash
# Check queue rules
/queue simple print
/queue tree print

# Temporarily disable queues for testing
/queue simple disable [queue-name]
```

---

## 📺 Scenario 3: "Video Buffering/Streaming Issues"

### Customer Report
*"Netflix/YouTube keeps buffering, but web browsing works fine"*

### Rapid Diagnosis (2 minutes)
```bash
# Step 1: Test sustained bandwidth
/tool bandwidth-test [upstream-gateway] protocol=tcp duration=60s

# Step 2: Check packet loss
/tool flood-ping [upstream-gateway] count=1000 size=1472

# Step 3: Check MTU issues
/ping [streaming-server] size=1472 do-not-fragment count=5
```

### Common Causes & Solutions

#### Cause 1: MTU Issues
**Symptoms:** Large packets fail, small packets work
```bash
# Test MTU sizes
/ping google.com size=1472 do-not-fragment count=5
/ping google.com size=1200 do-not-fragment count=5

# Solution: Adjust MTU
/interface ethernet set [wan-interface] mtu=1500
/ip firewall mangle add chain=forward action=change-mss new-mss=1460 protocol=tcp tcp-flags=syn
```

#### Cause 2: Buffer Bloat
**Symptoms:** High latency under load
```bash
# Test latency under load
/tool bandwidth-test [upstream-gateway] protocol=tcp direction=transmit duration=30s &
/ping [upstream-gateway] count=30

# Solution: Implement traffic shaping
/queue simple add target=[lan-interface] max-limit=80M/80M
```

#### Cause 3: Upstream Congestion
**Symptoms:** Speed varies by time of day
```bash
# Monitor over time
/tool graphing interface add interface=[wan-interface]

# Solution: Contact upstream provider or implement QoS
/queue tree add parent=global name=upload max-limit=50M
```

---

## 🔄 Scenario 4: "Intermittent Connectivity"

### Customer Report
*"Internet works sometimes, then stops working randomly"*

### Rapid Diagnosis (5 minutes)
```bash
# Step 1: Continuous monitoring
/ping [upstream-gateway] count=0 &

# Step 2: Check logs for patterns
/log print where topics~"error|critical" and time>="today"

# Step 3: Monitor interface statistics
/interface monitor-traffic [wan-interface] duration=300
```

### Common Causes & Solutions

#### Cause 1: Flapping Interface
**Symptoms:** Link goes up/down repeatedly
```bash
# Check interface logs
/log print where message~"[wan-interface]"

# Solution: Check physical connections, replace cable/SFP
```

#### Cause 2: DHCP Lease Issues
**Symptoms:** Periodic loss of IP address
```bash
# Check DHCP lease time
/ip dhcp-client print detail

# Solution: Set static IP or longer lease
/ip address add address=[static-ip/mask] interface=[wan-interface]
```

#### Cause 3: Overheating
**Symptoms:** Issues during hot periods
```bash
# Check system temperature
/system health print

# Solution: Improve ventilation, clean fans
```

---

## 🏠 Scenario 5: "Some Clients Can't Connect"

### Customer Report
*"Some devices work fine, others can't get internet"*

### Rapid Diagnosis (3 minutes)
```bash
# Step 1: Check DHCP pool
/ip pool print
/ip dhcp-server lease print

# Step 2: Test from router
/ping [problem-client-ip] count=5

# Step 3: Check firewall rules
/ip firewall filter print stats where action=drop and packets>0
```

### Common Causes & Solutions

#### Cause 1: DHCP Pool Exhausted
**Symptoms:** New devices can't get IP addresses
```bash
# Check available addresses
/ip dhcp-server lease print where status=bound

# Solution: Expand DHCP pool
/ip pool set [pool-name] ranges=************-*************
```

#### Cause 2: MAC Address Filtering
**Symptoms:** Specific devices blocked
```bash
# Check access list
/interface wireless access-list print
/ip firewall filter print where src-mac-address!=""

# Solution: Add device to allowed list
/interface wireless access-list add mac-address=[client-mac] action=accept
```

#### Cause 3: VLAN Issues
**Symptoms:** Devices on wrong VLAN
```bash
# Check VLAN assignments
/interface bridge port print where interface=[client-port]

# Solution: Correct VLAN assignment
/interface bridge port set [port] pvid=[correct-vlan]
```

---

## ⚡ Quick Reference: First Actions by Symptom

| Symptom | First Command | Expected Result |
|---------|---------------|-----------------|
| No internet | `/ping [gateway]` | Should respond |
| Slow speeds | `/interface monitor-traffic [wan]` | Check utilization |
| Buffering | `/ping [gateway] size=1472` | Check MTU |
| Intermittent | `/log print where topics~error` | Look for patterns |
| Some clients fail | `/ip dhcp-server lease print` | Check DHCP |

---

## 🎯 Pro Troubleshooting Tips

### 1. Always Establish Baseline
```bash
# Create performance baseline when system is working
/tool bandwidth-test [upstream] duration=30s
/ping [upstream] count=100
```

### 2. Use Continuous Monitoring
```bash
# Long-term ping for intermittent issues
/ping [target] count=0 > ping-results.txt

# Interface monitoring
/interface monitor-traffic [interface] duration=3600
```

### 3. Document Everything
- Time issue started
- Exact error messages
- Commands run and results
- Changes made

### 4. Test One Thing at a Time
- Make single changes
- Test after each change
- Revert if no improvement

This guide covers 90% of the issues you'll encounter as an ISP. Practice these scenarios to build your troubleshooting speed and confidence.
