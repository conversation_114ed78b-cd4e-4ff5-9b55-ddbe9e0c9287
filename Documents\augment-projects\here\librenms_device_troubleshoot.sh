#!/bin/bash

# LibreNMS Device Addition Troubleshooting Script
# This script helps diagnose why devices cannot be added to LibreNMS

echo "=== LibreNMS Device Addition Troubleshooting ==="
echo ""

# Get device IP and community string from user
read -p "Enter device IP address: " DEVICE_IP
read -p "Enter SNMP community string (default: public): " COMMUNITY
COMMUNITY=${COMMUNITY:-public}
read -p "Enter SNMP version (1, 2c, 3) [default: 2c]: " SNMP_VERSION
SNMP_VERSION=${SNMP_VERSION:-2c}

echo ""
echo "Testing device: $DEVICE_IP"
echo "Community: $COMMUNITY"
echo "SNMP Version: $SNMP_VERSION"
echo ""

# Test 1: Basic connectivity (ping)
echo "=== Test 1: Basic Connectivity (Ping) ==="
if ping -c 3 $DEVICE_IP > /dev/null 2>&1; then
    echo "✓ PING: Device is reachable"
else
    echo "✗ PING: Device is NOT reachable"
    echo "  Check network connectivity and firewall rules"
fi
echo ""

# Test 2: SNMP port connectivity
echo "=== Test 2: SNMP Port Connectivity ==="
if timeout 5 bash -c "</dev/tcp/$DEVICE_IP/161" 2>/dev/null; then
    echo "✓ PORT 161: SNMP port is open"
else
    echo "✗ PORT 161: SNMP port is closed or filtered"
    echo "  Check if SNMP service is running and firewall allows UDP 161"
fi
echo ""

# Test 3: Basic SNMP connectivity
echo "=== Test 3: Basic SNMP Connectivity ==="
if command -v snmpget >/dev/null 2>&1; then
    # Test system description (standard OID that should work on all devices)
    SNMP_RESULT=$(snmpget -v$SNMP_VERSION -c $COMMUNITY -t 5 -r 1 $DEVICE_IP *******.*******.0 2>/dev/null)
    if [ $? -eq 0 ] && [ ! -z "$SNMP_RESULT" ]; then
        echo "✓ SNMP: Basic SNMP query successful"
        echo "  System Description: $SNMP_RESULT"
    else
        echo "✗ SNMP: Basic SNMP query failed"
        echo "  Check community string and SNMP configuration"
    fi
else
    echo "⚠ SNMP tools not installed. Install with: apt-get install snmp snmp-mibs-downloader"
fi
echo ""

# Test 4: LibreNMS specific OIDs
echo "=== Test 4: LibreNMS Required OIDs ==="
if command -v snmpget >/dev/null 2>&1; then
    # Test sysObjectID (required by LibreNMS)
    echo "Testing sysObjectID (*******.*******.0)..."
    SYSOBJECTID=$(snmpget -v$SNMP_VERSION -c $COMMUNITY -t 5 -r 1 $DEVICE_IP *******.*******.0 2>/dev/null)
    if [ $? -eq 0 ] && [ ! -z "$SYSOBJECTID" ]; then
        echo "✓ sysObjectID: $SYSOBJECTID"
    else
        echo "✗ sysObjectID: Failed - This is required by LibreNMS"
        echo "  Device may not support standard SNMP MIBs"
    fi
    
    # Test sysUpTime
    echo "Testing sysUpTime (*******.*******.0)..."
    SYSUPTIME=$(snmpget -v$SNMP_VERSION -c $COMMUNITY -t 5 -r 1 $DEVICE_IP *******.*******.0 2>/dev/null)
    if [ $? -eq 0 ] && [ ! -z "$SYSUPTIME" ]; then
        echo "✓ sysUpTime: $SYSUPTIME"
    else
        echo "✗ sysUpTime: Failed"
    fi
fi
echo ""

# Test 5: SNMP Walk test
echo "=== Test 5: SNMP Walk Test ==="
if command -v snmpwalk >/dev/null 2>&1; then
    echo "Testing SNMP walk on system tree..."
    WALK_RESULT=$(timeout 10 snmpwalk -v$SNMP_VERSION -c $COMMUNITY $DEVICE_IP *******.2.1.1 2>/dev/null | head -5)
    if [ ! -z "$WALK_RESULT" ]; then
        echo "✓ SNMP Walk: Successful"
        echo "Sample output:"
        echo "$WALK_RESULT"
    else
        echo "✗ SNMP Walk: Failed or no response"
    fi
else
    echo "⚠ SNMP tools not installed"
fi
echo ""

# Recommendations
echo "=== Recommendations ==="
echo ""
echo "If tests failed, check the following:"
echo ""
echo "1. Device SNMP Configuration:"
echo "   - SNMP service is enabled"
echo "   - Community string matches"
echo "   - SNMP version is correct"
echo "   - Access control lists allow LibreNMS server IP"
echo ""
echo "2. Network/Firewall:"
echo "   - UDP port 161 is open"
echo "   - No firewall blocking SNMP traffic"
echo "   - Network routing is correct"
echo ""
echo "3. LibreNMS Configuration:"
echo "   - Check LibreNMS logs: tail -f /opt/librenms/logs/librenms.log"
echo "   - Verify SNMP settings in LibreNMS config"
echo "   - Try 'Force Add' option if basic connectivity works"
echo ""
echo "4. For MikroTik devices specifically:"
echo "   - Enable SNMP: /snmp set enabled=yes"
echo "   - Set community: /snmp community add name=public"
echo "   - Check contact and location are set"
echo ""
