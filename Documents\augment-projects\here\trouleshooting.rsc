🧪 Layered Connectivity & Link Troubleshooting Toolkit
🔹 Layer 1: Physical Interface Status
/interface print
/interface ethernet monitor <interface-name>


- Confirms link status, RX/TX rates, cable issues

🔹 Layer 2: VLAN & MAC Visibility
/interface vlan print
/interface bridge vlan print
/tool mac-scan interface=<uplink-port>
/ip arp print where interface=<vlan-interface>


- Validates VLAN tagging and MAC discovery
- Confirms ARP resolution for peer IPs

🔹 Layer 3: IP Assignment & Ping
/ip address print where interface=<vlan-interface>
ping <peer-ip>
ping <local-ip>
/tool traceroute <peer-ip>


- Verifies IP stack and reachability
- Traceroute helps isolate routing or firewall blocks

🔹 Firewall & Service Access
/ip firewall filter print
/ip service print where name=winbox
/ip service set winbox address=0.0.0.0/0


- Confirms Winbox and ICMP access
- Adjusts available-from if needed

🔹 Routing Table & Gateway Checks
/ip route print
/ip route check <destination-ip>


- Ensures correct routing paths
- Diagnoses “no route to host” issues

🔹 Bridge-Level Diagnostics
/interface bridge monitor <bridge-name>
/interface bridge port print


- Confirms port status and bridge membership

🔹 Traffic & Flow Analysis
/tool torch interface=<vlan-interface>
/tool sniffer quick interface=<vlan-interface>


- Real-time traffic visibility
- Useful for confirming packet flow and protocol activity

🔹 Cleanup & Recovery
/ip address remove [find address=<temporary-ip>]
/ip arp remove [find where address=<peer-ip>]
/ip firewall filter remove [find where comment~"temporary"]


- Safely remove test IPs and rules after diagnostics




Network Asymmetric Bandwidth Troubleshooting Guide 
Author: Sharon – Network Support Engineer 
Date: August 28, 2025 
Step 1: Initial Assessment 
Purpose: Understand the problem scope and gather basic information 
Command: 
/interface monitor-traffic [interface-name] duration=60  
Look For: 
• TX vs RX bit rates (asymmetric pattern) 
• Packet drops or errors 
• FastPath usage (fp-tx/fp-rx should match tx/rx for physical interfaces) 
Red Flags: 
• Large TX/RX ratio (e.g., 40Mbps TX vs 4Mbps RX) 
• FastPath TX = 0bps on physical interfaces 
• High error/drop rates 
Cross-Check: 
Use LibreNMS graphs to identify historical patterns (e.g., spikes at 09:00–10:12 AM EAT). 
Note current time: 12:40 PM EAT, August 28, 2025 
Step 2: Identify Interface Type 
Purpose: Different interface types have different limitations 
Commands: 
/interface print where name="[interface-name]" /interface vlan print detail where 
name="[interface-name]" /interface ethernet print detail where name="[interface-name]"  
Key Points: 
• VLAN interfaces: Cannot use FastPath for TX (normal behavior) 
• Physical interfaces: Should use FastPath for both TX/RX 
• Bridge interfaces: May have FastPath limitations 
•  
Check Link-Down History: 
/interface print stats-detail where name="[interface-name]"  
Step 3: Physical Layer Verification 
Purpose: Ensure physical connectivity is optimal 
Command: 
/interface ethernet monitor [physical-interface] once  
Look For: 
• status: link-ok 
• full-duplex: yes 
• auto-negotiation: done 
• rate: Expected speed (1Gbps, 10Gbps, etc.) 
Common Issues: 
• Duplex mismatch 
• Speed negotiation problems 
• Cable or SFP issues 
Log Inspection: 
/log print where topics~"interface" or topics~"link"  
SFP Health Check (if applicable): 
/interface ethernet monitor [interface-name] once  
Step 4: Check Customer Bandwidth Allocation 
Purpose: Verify if ISP-side limits are causing the issue 
Commands: 
/queue simple print where target~"[customer-name]" /queue simple print where 
target~"[interface-name]" /queue simple print stats where name="[queue-name]"  
Look For: 
• max-limit vs current rate 
• queued-packets (should be 0 if not hitting limits) 
• dropped packets (indicates customer equipment issues) 
Analysis: 
• Current rate << max-limit → Customer equipment limitation 
• High dropped packets → Customer cannot handle traffic 
• Queued packets > 0 → Bandwidth limits being hit 
Step 5: Network Connectivity Tests 
Purpose: Verify end-to-end connectivity and identify bottlenecks 
Commands: 
/ip address print where interface="[interface-name]" /ping [customer-ip] count=5 /ip arp 
print where interface="[interface-name]" /tool torch interface="[interface-name]" 
duration=30  
Interpretation: 
• Ping timeouts: Normal if customer blocks ICMP 
• ARP "incomplete": Device connectivity issues 
• Torch shows different IPs: Unexpected traffic sources 
Bandwidth Test: 
/tool bandwidth-test [customer-ip] protocol=tcp direction=both duration=20  
Final Notes 
• Document findings and timestamps for client reporting 
• If issue persists, escalate with annotated logs and interface stats 
• Consider scripting repetitive checks for future automation 