# Monitoring & Proactive Detection - Catch Issues Before Customers Call

## 🎯 The Proactive ISP Philosophy

**Goal:** Detect and fix issues before customers notice them  
**Method:** Automated monitoring with intelligent alerting  
**Result:** Reduced support calls and improved customer satisfaction  

---

## 📊 Essential Monitoring Metrics

### 1. Interface Health Monitoring
```bash
# Enable SNMP for external monitoring
/snmp set enabled=yes contact="<EMAIL>" location="DataCenter-1"

# Create interface monitoring script
/system script add name="interface-monitor" source={
    :foreach i in=[/interface find where type="ether"] do={
        :local iname [/interface get $i name]
        :local running [/interface get $i running]
        :local rxerrors [/interface get $i rx-error]
        :local txerrors [/interface get $i tx-error]
        
        :if (!$running) do={
            :log error ("ALERT: Interface " . $iname . " is DOWN")
        }
        :if ($rxerrors > 100) do={
            :log warning ("ALERT: High RX errors on " . $iname . ": " . $rxerrors)
        }
        :if ($txerrors > 100) do={
            :log warning ("ALERT: High TX errors on " . $iname . ": " . $txerrors)
        }
    }
}

# Schedule every 2 minutes
/system scheduler add name="interface-check" interval=2m on-event="interface-monitor"
```

### 2. Bandwidth Utilization Monitoring
```bash
# Monitor WAN utilization
/system script add name="bandwidth-monitor" source={
    :local wanint "ether1-wan"
    :local rxrate [/interface get $wanint rx-bits-per-second]
    :local txrate [/interface get $wanint tx-bits-per-second]
    :local maxrate 1000000000
    
    :local rxpercent (($rxrate * 100) / $maxrate)
    :local txpercent (($txrate * 100) / $maxrate)
    
    :if ($rxpercent > 80) do={
        :log warning ("ALERT: WAN RX utilization high: " . $rxpercent . "%")
    }
    :if ($txpercent > 80) do={
        :log warning ("ALERT: WAN TX utilization high: " . $txpercent . "%")
    }
}

# Run every 5 minutes
/system scheduler add name="bandwidth-check" interval=5m on-event="bandwidth-monitor"
```

### 3. Upstream Connectivity Monitoring
```bash
# Continuous upstream monitoring
/system script add name="upstream-monitor" source={
    :local gateway "***********"
    :local testhost "*******"
    
    # Test gateway connectivity
    :local gwping [/ping $gateway count=3]
    :if ($gwping < 3) do={
        :log error ("ALERT: Gateway " . $gateway . " unreachable")
    }
    
    # Test internet connectivity
    :local inet [/ping $testhost count=3]
    :if ($inet < 3) do={
        :log error ("ALERT: Internet connectivity issue")
    }
    
    # Test DNS resolution
    :do {
        /ping "google.com" count=1
    } on-error={
        :log error ("ALERT: DNS resolution failure")
    }
}

# Run every minute
/system scheduler add name="upstream-check" interval=1m on-event="upstream-monitor"
```

---

## 🚨 Intelligent Alerting System

### Email Alert Configuration
```bash
# Configure email settings
/tool e-mail set server=smtp.gmail.com port=587 start-tls=yes \
    user="<EMAIL>" password="your-app-password" \
    from="<EMAIL>"

# Create email alert script
/system script add name="send-alert" source={
    :local subject $1
    :local body $2
    :local priority $3
    
    :if ($priority = "critical") do={
        /tool e-mail send to="<EMAIL>,<EMAIL>" \
            subject=("[CRITICAL] " . $subject) body=$body
    } else={
        /tool e-mail send to="<EMAIL>" \
            subject=("[WARNING] " . $subject) body=$body
    }
}
```

### Smart Alert Filtering
```bash
# Prevent alert spam
/system script add name="smart-alert" source={
    :local alerttype $1
    :local message $2
    :local lastfile ("last-" . $alerttype . ".txt")
    
    # Check if same alert was sent in last 15 minutes
    :local lastalert [/file get $lastfile contents]
    :local currenttime [/system clock get time]
    
    # Only send if different from last alert or >15 minutes passed
    :if ($lastalert != $message) do={
        /file set $lastfile contents=$message
        /system script run send-alert $alerttype $message "warning"
    }
}
```

---

## 📈 Performance Trending

### Historical Data Collection
```bash
# Enable graphing for key interfaces
/tool graphing interface add interface=ether1-wan store-on-disk=yes
/tool graphing interface add interface=bridge-lan store-on-disk=yes

# System resource graphing
/tool graphing resource add store-on-disk=yes

# Custom performance logging
/system script add name="performance-log" source={
    :local timestamp [/system clock get date] . " " . [/system clock get time]
    :local cpuload [/system resource get cpu-load]
    :local freemem [/system resource get free-memory]
    :local uptime [/system resource get uptime]
    
    :local logentry ($timestamp . ",CPU:" . $cpuload . "%,FreeMem:" . $freemem . ",Uptime:" . $uptime)
    :log info $logentry
}

# Log performance every 10 minutes
/system scheduler add name="perf-log" interval=10m on-event="performance-log"
```

---

## 🔍 Customer Experience Monitoring

### End-to-End Connectivity Tests
```bash
# Test customer subnets
/system script add name="customer-monitor" source={
    :local customers {"************/24";"************/24";"************/24"}
    
    :foreach subnet in=$customers do={
        :local testip [:pick $subnet 0 ([:find $subnet "/"] - 1)]
        :set testip ([:pick $testip 0 ([:len $testip] - 1)] . "1")
        
        :local result [/ping $testip count=3]
        :if ($result < 2) do={
            :log warning ("ALERT: Customer subnet " . $subnet . " connectivity issue")
        }
    }
}

# Check customer connectivity every 5 minutes
/system scheduler add name="customer-check" interval=5m on-event="customer-monitor"
```

### DNS Performance Monitoring
```bash
# Monitor DNS response times
/system script add name="dns-monitor" source={
    :local dnsservers {"*******";"*******";"**************"}
    :local testdomain "google.com"
    
    :foreach dns in=$dnsservers do={
        :local starttime [/system clock get time]
        :do {
            /tool nslookup $testdomain $dns
        } on-error={
            :log warning ("ALERT: DNS server " . $dns . " not responding")
        }
    }
}

# Test DNS every 3 minutes
/system scheduler add name="dns-check" interval=3m on-event="dns-monitor"
```

---

## 📱 Mobile Monitoring Dashboard

### SNMP Monitoring Setup
```bash
# Configure SNMP for external monitoring tools
/snmp set enabled=yes
/snmp community add name="public" read-access=yes

# Key SNMP OIDs to monitor:
# *******.*******.1.8.X - Interface operational status
# *******.*******.1.10.X - Interface bytes in
# *******.*******.1.16.X - Interface bytes out
# *******.4.1.14988.*******.1.0 - CPU load
# *******.4.1.14988.*******.1.0 - Free memory
```

### Web-Based Status Page
```bash
# Create simple status page
/system script add name="status-page" source={
    :local html "<!DOCTYPE html><html><head><title>ISP Status</title></head><body>"
    :set html ($html . "<h1>Network Status</h1>")
    :set html ($html . "<p>Last Update: " . [/system clock get time] . "</p>")
    
    # Interface status
    :set html ($html . "<h2>Interfaces</h2><ul>")
    :foreach i in=[/interface find where type="ether"] do={
        :local iname [/interface get $i name]
        :local running [/interface get $i running]
        :local status "DOWN"
        :if ($running) do={ :set status "UP" }
        :set html ($html . "<li>" . $iname . ": " . $status . "</li>")
    }
    :set html ($html . "</ul></body></html>")
    
    /file set "status.html" contents=$html
}

# Update status page every minute
/system scheduler add name="status-update" interval=1m on-event="status-page"
```

---

## 🎛️ Automated Response System

### Self-Healing Scripts
```bash
# Auto-restart failed interfaces
/system script add name="auto-heal-interface" source={
    :foreach i in=[/interface find where running=no and type="ether"] do={
        :local iname [/interface get $i name]
        :log warning ("Auto-healing interface: " . $iname)
        
        /interface ethernet set $i disabled=yes
        :delay 5s
        /interface ethernet set $i disabled=no
        :delay 10s
        
        :local newstatus [/interface get $i running]
        :if ($newstatus) do={
            :log info ("Successfully restored interface: " . $iname)
        } else={
            :log error ("Failed to restore interface: " . $iname)
        }
    }
}

# Auto-restart DHCP client on WAN issues
/system script add name="auto-heal-wan" source={
    :local wanint "ether1-wan"
    :local gateway "***********"
    
    :local pingresult [/ping $gateway count=3]
    :if ($pingresult = 0) do={
        :log warning ("WAN connectivity lost, attempting DHCP renewal")
        /ip dhcp-client renew $wanint
        :delay 30s
        
        :local newping [/ping $gateway count=3]
        :if ($newping > 0) do={
            :log info ("WAN connectivity restored via DHCP renewal")
        } else={
            :log error ("WAN connectivity still down after DHCP renewal")
        }
    }
}
```

---

## 📋 Monitoring Checklist

### Daily Automated Checks
- [ ] All interfaces operational
- [ ] Upstream connectivity stable
- [ ] Bandwidth utilization normal
- [ ] No critical errors in logs
- [ ] Customer subnets reachable
- [ ] DNS resolution working

### Weekly Manual Reviews
- [ ] Review performance trends
- [ ] Check for recurring issues
- [ ] Update monitoring thresholds
- [ ] Test alert mechanisms
- [ ] Review customer complaints vs. monitoring data

### Monthly Optimization
- [ ] Analyze false positive alerts
- [ ] Adjust monitoring sensitivity
- [ ] Add new monitoring points
- [ ] Review SLA compliance
- [ ] Update escalation procedures

---

## 🚀 Advanced Monitoring Tools

### Integration with External Tools
```bash
# Syslog export for centralized logging
/system logging add topics=error,critical,warning action=remote remote=***********00

# SNMP trap configuration
/snmp set trap-version=2 trap-community=public trap-target=***********00
```

### Recommended External Tools
- **LibreNMS** - Free network monitoring
- **PRTG** - Commercial monitoring solution
- **Zabbix** - Enterprise monitoring platform
- **Grafana + InfluxDB** - Custom dashboards

This monitoring framework will transform you from reactive to proactive, catching issues before they impact customers and building a reputation for reliable service.
