# ========================================
# MIKROTIK NETWORK TROUBLESHOOTING GUIDE
# Author: Sharon - Network Support Engineer
# Updated: December 2024
# ========================================

# ========================================
# SECTION 1: DEVICE CONNECTIVITY ISSUES
# ========================================

## WHEN TO USE THIS SECTION:
# - Device visible in MAC scan but not responding to ping
# - Winbox can connect via MAC but not IP address
# - Device appears offline but is physically connected
# - ARP shows "failed" or "incomplete" status
# - Management interfaces not accessible
# SYMPTOMS: Device discovery works, but no Layer 3 communication

## STEP 1: BASIC CONNECTIVITY CHECK
# Purpose: Verify if device is reachable and responding

# Test basic ping connectivity
/ping <target-ip> count=5

# Check if device responds to different IPs in same subnet
/ping <subnet-ip-1> count=3
/ping <subnet-ip-2> count=3

## STEP 2: LAYER 2 VERIFICATION
# Purpose: Confirm device presence at MAC level

# Scan for MAC addresses on interface
/tool mac-scan interface=<interface-name>
# Look for: Target MAC address and IP assignment

# Check ARP table for device
/ip arp print where address=<target-ip>
# Status should be: "reachable" or "complete"
# If "failed" or "incomplete" - ARP issue exists

## STEP 3: INTERFACE CONFIGURATION CHECK
# Purpose: Verify interface setup and IP addressing

# Check interface status and configuration
/interface print where name=<interface-name>
/interface print stats where name=<interface-name>

# Verify IP address configuration
/ip address print where interface=<interface-name>

# For VLAN interfaces, check VLAN configuration
/interface vlan print where name=<interface-name>

## STEP 4: BRIDGE CONFIGURATION (If Applicable)
# Purpose: Check if interface is bridged and bridge status

# Check if interface is bridge slave
/interface bridge port print where interface=<interface-name>

# If bridged, check bridge IP addresses
/ip address print where interface=<bridge-name>

# Check bridge ARP table
/ip arp print where interface=<bridge-name>

## STEP 5: ARP RESOLUTION FIXES
# Purpose: Resolve ARP-related connectivity issues

# Remove failed ARP entries
/ip arp remove [find address=<target-ip>]

# Add static ARP entry (use correct interface - bridge if bridged)
/ip arp add address=<target-ip> mac-address=<target-mac> interface=<correct-interface>

# Verify static ARP entry
/ip arp print where address=<target-ip>

# Test connectivity after ARP fix
/ping <target-ip> count=5

## STEP 6: MANAGEMENT ACCESS TESTING
# Purpose: Test different management protocols

# Test web interface
# Browser: http://<target-ip>

# Test Winbox (try both methods)
# Winbox: Connect to <target-ip>
# Winbox: Connect to <target-mac>

# Test SSH
/system ssh <target-ip>

# Test Telnet
/system telnet <target-ip>

# ========================================
# SECTION 2: ASYMMETRIC BANDWIDTH TROUBLESHOOTING
# Author: Sharon - Network Support Engineer
# ========================================

## WHEN TO USE THIS SECTION:
# - Customer reports slow speeds in one direction
# - Upload much faster than download (or vice versa)
# - Interface shows TX/RX imbalance in monitoring
# - FastPath shows 0bps on physical interfaces
# - Speed test results don't match allocated bandwidth
# SYMPTOMS: Uneven traffic flow, performance complaints

## STEP 1: INITIAL BANDWIDTH ASSESSMENT
# Purpose: Understand the problem scope and gather basic information

# Monitor interface traffic for asymmetric patterns
/interface monitor-traffic <interface-name> duration=60

# Look for RED FLAGS:
# - Large TX/RX ratio (e.g., 40Mbps TX vs 4Mbps RX)
# - FastPath TX = 0bps on physical interfaces
# - High error/drop rates
# - Packet drops or errors

# Check current interface statistics
/interface print stats where name=<interface-name>

# Cross-check with monitoring graphs (LibreNMS/PRTG)
# Note: Check historical patterns for time-based issues

## STEP 2: IDENTIFY INTERFACE TYPE & LIMITATIONS
# Purpose: Different interface types have different FastPath capabilities

# Check interface type and configuration
/interface print where name=<interface-name>
/interface vlan print detail where name=<interface-name>
/interface ethernet print detail where name=<interface-name>

# KEY POINTS:
# - VLAN interfaces: Cannot use FastPath for TX (normal behavior)
# - Physical interfaces: Should use FastPath for both TX/RX
# - Bridge interfaces: May have FastPath limitations

# Check link-down history
/interface print stats-detail where name=<interface-name>

## STEP 3: PHYSICAL LAYER VERIFICATION
# Purpose: Ensure physical connectivity is optimal

# Check ethernet interface status
/interface ethernet monitor <interface-name> once

# Verify CRITICAL parameters:
# - status: link-ok
# - full-duplex: yes
# - auto-negotiation: done
# - rate: Expected speed (1Gbps, 10Gbps, etc.)

# Common issues to check:
# - Duplex mismatch
# - Speed negotiation problems
# - Cable or SFP issues

# Check system logs for interface issues
/log print where topics~"interface" or topics~"link"

# SFP health check (if applicable)
/interface ethernet monitor <interface-name> once

## STEP 4: CUSTOMER BANDWIDTH ALLOCATION CHECK
# Purpose: Verify if ISP-side limits are causing the issue

# Check simple queues for customer
/queue simple print where target~<customer-name>
/queue simple print where target~<interface-name>
/queue simple print stats where name=<queue-name>

# ANALYSIS CRITERIA:
# - max-limit vs current rate
# - queued-packets (should be 0 if not hitting limits)
# - dropped packets (indicates customer equipment issues)

# INTERPRETATION:
# - Current rate << max-limit → Customer equipment limitation
# - High dropped packets → Customer cannot handle traffic
# - Queued packets > 0 → Bandwidth limits being hit

## STEP 5: COMPREHENSIVE CONNECTIVITY TESTS
# Purpose: Verify end-to-end connectivity and identify bottlenecks

# Check IP configuration
/ip address print where interface=<interface-name>

# Test basic connectivity
/ping <customer-ip> count=5

# Check ARP resolution
/ip arp print where interface=<interface-name>

# Real-time traffic analysis
/tool torch interface=<interface-name> duration=30

# INTERPRETATION:
# - Ping timeouts: Normal if customer blocks ICMP
# - ARP "incomplete": Device connectivity issues
# - Torch shows different IPs: Unexpected traffic sources

# Bandwidth testing
/tool bandwidth-test <customer-ip> protocol=tcp direction=both duration=20

## STEP 6: ADVANCED TRAFFIC ANALYSIS
# Purpose: Deep dive into traffic patterns

# Packet capture for detailed analysis
/tool sniffer quick interface=<interface-name> duration=30

# Monitor specific protocols
/tool torch interface=<interface-name> protocol=tcp duration=30
/tool torch interface=<interface-name> protocol=udp duration=30

# Check for broadcast storms or unusual traffic
/tool torch interface=<interface-name> src-address=0.0.0.0/0 duration=30

## STEP 7: DOCUMENTATION & ESCALATION
# Purpose: Document findings for client reporting

# Document findings with timestamps:
# - Interface type and limitations
# - Physical layer status
# - Queue configuration and utilization
# - Traffic patterns observed
# - Customer equipment behavior

# If issue persists:
# - Escalate with annotated logs and interface stats
# - Consider scripting repetitive checks for automation

# ========================================
# SECTION 3: ROUTING & FIREWALL ISSUES
# ========================================

## WHEN TO USE THIS SECTION:
# - Device accessible but specific services unreachable
# - Intermittent connectivity issues
# - New network segments not communicating
# - Management services suddenly stop working
# - Traffic not following expected paths
# SYMPTOMS: Partial connectivity, service-specific failures

## STEP 1: ROUTING TABLE VERIFICATION
# Purpose: Ensure correct routing paths exist

# Check routing table
/ip route print

# Check specific route to destination
/ip route check <destination-ip>

# Verify default gateway
/ip route print where dst-address=0.0.0.0/0

## STEP 2: FIREWALL RULE ANALYSIS
# Purpose: Identify blocking firewall rules

# Check firewall filter rules
/ip firewall filter print

# Look for invalid rules (marked with "I")
/ip firewall filter remove [find invalid]

# Check NAT rules
/ip firewall nat print

## STEP 3: SERVICE ACCESSIBILITY
# Purpose: Verify management services are enabled

# Check IP services
/ip service print

# Enable Winbox if disabled
/ip service enable winbox

# Check MAC server settings
/tool mac-server print
/tool mac-server mac-winbox print

# ========================================
# SECTION 4: CLEANUP & RECOVERY PROCEDURES
# ========================================

## WHEN TO USE THIS SECTION:
# - After completing any troubleshooting session
# - Before making major configuration changes
# - When temporary fixes need to be made permanent
# - After accidentally breaking network connectivity
# - Need to document changes for handover
# SYMPTOMS: Post-troubleshooting maintenance

## STEP 1: REMOVE TEMPORARY CONFIGURATIONS
# Purpose: Clean up test configurations safely

# Remove temporary IP addresses
/ip address remove [find comment~"temp"]

# Remove failed ARP entries
/ip arp remove [find status="failed"]

# Remove temporary firewall rules
/ip firewall filter remove [find comment~"temp"]

## STEP 2: RESTORE ORIGINAL CONFIGURATION
# Purpose: Revert to known working state

# Backup current configuration before changes
/export file=backup-before-changes

# Document all changes made during troubleshooting
# Keep log of:
# - Commands executed
# - Results observed
# - Final resolution

## STEP 3: VERIFICATION AFTER FIXES
# Purpose: Confirm issues are resolved

# Test basic connectivity
/ping <target-ip> count=5

# Verify management access
# Test: Winbox, SSH, Web interface

# Check interface statistics
/interface print stats where name=<interface-name>

# Monitor for stability
/interface monitor-traffic <interface-name> duration=30

# ========================================
# SECTION 5: COMMON ISSUE QUICK FIXES
# ========================================

## WHEN TO USE THIS SECTION:
# - You recognize a familiar problem pattern
# - Need immediate temporary resolution
# - Time-critical issues requiring fast fixes
# - Standard problems with known solutions
# SYMPTOMS: Recurring issues with established solutions

## DECISION TREE:
# 1. Can't reach device at all? → Section 1
# 2. Device reachable but slow/uneven speeds? → Section 2
# 3. Partial connectivity/service issues? → Section 3
# 4. Need to clean up after troubleshooting? → Section 4
# 5. Recognize the problem? → Section 5

## ARP Resolution Issues:
/ip arp remove [find address=<ip>]
/ip arp add address=<ip> mac-address=<mac> interface=<interface>

## Bridge vs VLAN Interface Confusion:
# Always use bridge interface for ARP when VLAN is bridged
# Check: /interface bridge port print

## Invalid Firewall Rules:
/ip firewall filter remove [find invalid]

## Interface Not Responding:
/interface disable <interface-name>
:delay 2
/interface enable <interface-name>

## MAC Server Issues:
/tool mac-server set [find] disabled=no
/tool mac-server mac-winbox set [find] disabled=no

# ========================================
# TROUBLESHOOTING DECISION TREE
# ========================================

# 1. Can you ping the device?
#    NO → Check Layer 2 (MAC scan, ARP)
#    YES → Check management services

# 2. Does MAC scan show the device?
#    NO → Physical/VLAN configuration issue
#    YES → ARP resolution problem

# 3. Is ARP status "failed" or "incomplete"?
#    YES → Add static ARP entry
#    NO → Check firewall/services

# 4. Is interface bridged?
#    YES → Use bridge interface for ARP
#    NO → Use VLAN interface directly

# 5. Can you access web interface?
#    YES → Winbox/SSH service issue
#    NO → Device firewall blocking access

# 6. Is bandwidth asymmetric?
#    YES → Check FastPath, interface type, queues
#    NO → Check physical layer and customer equipment

# 7. Are there packet drops?
#    YES → Check queues, physical layer, customer capacity
#    NO → Check for routing or firewall issues

# ========================================
# END OF TROUBLESHOOTING GUIDE
# ========================================