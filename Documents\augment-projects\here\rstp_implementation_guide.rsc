# RSTP Implementation Guide - Step by Step
# Follow this guide exactly for hands-on implementation

# ============================================================================
# STEP 1: Check Current Configuration (ALWAYS START HERE)
# ============================================================================

# First, let's see what we're working with
# Run these commands to check current state:

# Check existing bridges
/interface bridge print

# Check existing VLANs  
/interface vlan print

# Check interface status
/interface print where type=ether

# Check current IP addresses
/ip address print

# ============================================================================
# STEP 2: Clean Slate (Optional - only if needed)
# ============================================================================

# If you have existing bridge config that conflicts, clean it:
# WARNING: This will disconnect you if you're connected via bridge!

# Remove existing bridge VLANs (if any)
# /interface vlan remove [find]

# Remove existing bridge (if any) 
# /interface bridge remove [find]

# ============================================================================
# STEP 3: Create the Main RSTP Bridge
# ============================================================================

# Create bridge with RSTP enabled
/interface bridge add name=bridge-main protocol-mode=rstp priority=0x1000

# Verify bridge was created
/interface bridge print

# Expected output: You should see bridge-main with protocol-mode=rstp

# ============================================================================
# STEP 4: Add Physical Interfaces to Bridge
# ============================================================================

# Add ethernet interfaces with strategic priorities
# Adjust interface names based on your device (ether1, ether2, etc.)

# Primary path (lowest priority number = highest priority)
/interface bridge port add bridge=bridge-main interface=ether1 priority=0x10 path-cost=10

# Secondary path  
/interface bridge port add bridge=bridge-main interface=ether2 priority=0x20 path-cost=100

# Add more interfaces if you have them
# /interface bridge port add bridge=bridge-main interface=ether3 priority=0x30 path-cost=200

# Verify ports were added
/interface bridge port print

# Expected output: You should see your interfaces listed with their priorities

# ============================================================================
# STEP 5: Create Test VLANs
# ============================================================================

# Create VLAN interfaces on the bridge
/interface vlan add interface=bridge-main name=vlan10-users vlan-id=10
/interface vlan add interface=bridge-main name=vlan20-servers vlan-id=20
/interface vlan add interface=bridge-main name=vlan99-mgmt vlan-id=99

# Verify VLANs were created
/interface vlan print

# Expected output: You should see your VLAN interfaces

# ============================================================================
# STEP 6: Assign IP Addresses for Testing
# ============================================================================

# Assign IP addresses to VLANs so we can test connectivity
/ip address add address=************/24 interface=vlan10-users
/ip address add address=************/24 interface=vlan20-servers  
/ip address add address=************/24 interface=vlan99-mgmt

# Verify IP addresses
/ip address print

# Expected output: You should see IP addresses assigned to VLAN interfaces

# ============================================================================
# STEP 7: Check RSTP Status
# ============================================================================

# Check bridge port status and STP roles
/interface bridge port print detail

# Monitor specific port STP state (replace ether1 with your interface)
/interface bridge port monitor [find interface=ether1]

# Expected output: You should see port roles (root, designated, alternate)

# ============================================================================
# STEP 8: Basic Connectivity Test
# ============================================================================

# Test that VLANs are working
/ping ************ count=3
/ping ************ count=3

# Expected output: Successful pings

# ============================================================================
# STEP 9: Enable VLAN Filtering (Advanced - Optional for now)
# ============================================================================

# WARNING: This can break connectivity if not configured properly
# Only do this if you understand VLAN filtering

# /interface bridge set bridge-main vlan-filtering=yes

# If you enable VLAN filtering, you need to configure the VLAN table:
# /interface bridge vlan add bridge=bridge-main tagged=ether1,ether2,bridge-main vlan-ids=10
# /interface bridge vlan add bridge=bridge-main tagged=ether1,ether2,bridge-main vlan-ids=20
# /interface bridge vlan add bridge=bridge-main tagged=ether1,ether2,bridge-main vlan-ids=99

# ============================================================================
# STEP 10: Test Failover (The Fun Part!)
# ============================================================================

# Method 1: Disable interface to simulate cable disconnect
# /interface disable ether1

# Watch what happens:
# /interface bridge port monitor [find interface=ether2]

# You should see ether2 change from "alternate" to "root" or "designated"

# Re-enable the interface:
# /interface enable ether1

# Method 2: Change path costs to force failover
# /interface bridge port set [find interface=ether1] path-cost=1000
# /interface bridge port set [find interface=ether2] path-cost=10

# Reset costs:
# /interface bridge port set [find interface=ether1] path-cost=10  
# /interface bridge port set [find interface=ether2] path-cost=100

# ============================================================================
# VERIFICATION COMMANDS (Run these to check everything)
# ============================================================================

# 1. Bridge overview
/interface bridge print detail

# 2. Port status and roles
/interface bridge port print detail

# 3. VLAN status
/interface vlan print

# 4. IP connectivity
/ip address print

# 5. STP topology
/interface bridge port monitor [find interface=ether1]

# 6. Check logs for STP events
/log print where topics~"bridge"

# ============================================================================
# TROUBLESHOOTING COMMON ISSUES
# ============================================================================

# Issue: Can't connect to device after bridge changes
# Solution: Connect via different interface or console

# Issue: VLANs not working
# Solution: Check if VLAN filtering is enabled and properly configured

# Issue: No failover happening
# Solution: Check that you have redundant physical connections

# Issue: Slow convergence
# Solution: Verify RSTP is enabled (not classic STP)

# ============================================================================
# NEXT STEPS AFTER BASIC IMPLEMENTATION
# ============================================================================

# 1. Add more switches to create redundant topology
# 2. Configure edge ports for end devices
# 3. Test with real network traffic
# 4. Monitor convergence times
# 5. Implement monitoring scripts
# 6. Document your topology
