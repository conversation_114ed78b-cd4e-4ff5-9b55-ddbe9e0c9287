# Minimal MikroTik Configuration for Basic Internet Connectivity
# Apply this configuration to get basic connectivity working first

# Reset to minimal config (CAREFUL - this will remove existing config)
# /system reset-configuration no-defaults=yes skip-backup=yes

# Basic interface setup
/interface bridge
add name=bridge1

# Add LAN ports to bridge (keeping ether1 as WAN)
/interface bridge port
add bridge=bridge1 interface=ether2
add bridge=bridge1 interface=ether3
add bridge=bridge1 interface=ether4
add bridge=bridge1 interface=ether5

# Set LAN IP address
/ip address
add address=***********/24 interface=bridge1

# Configure DHCP client on WAN interface
/ip dhcp-client
add interface=ether1 disabled=no

# Configure DNS
/ip dns
set allow-remote-requests=yes servers=*******,*******

# Create DHCP server for LAN
/ip pool
add name=dhcp_pool ranges=*************-*************

/ip dhcp-server
add address-pool=dhcp_pool interface=bridge1 name=dhcp1

/ip dhcp-server network
add address=***********/24 gateway=*********** dns-server=***********

# NAT rule for internet access
/ip firewall nat
add action=masquerade chain=srcnat out-interface=ether1

# Basic firewall rules
/ip firewall filter
add action=accept chain=input connection-state=established,related
add action=accept chain=input src-address=***********/24
add action=drop chain=input
add action=accept chain=forward connection-state=established,related
add action=accept chain=forward src-address=***********/24
add action=drop chain=forward

# Test connectivity
/ping ******* count=5